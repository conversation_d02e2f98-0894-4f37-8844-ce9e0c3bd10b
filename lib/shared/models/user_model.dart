import 'package:equatable/equatable.dart';
import 'subscription_tier.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String username;
  final String gender;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isOnboardingComplete;
  final SubscriptionTier subscriptionTier;
  final DateTime? subscriptionEndDate;
  final int dailyMessageCount;
  final int dailyImageCount;
  final DateTime lastUsageReset;

  const UserModel({
    required this.id,
    required this.email,
    required this.username,
    required this.gender,
    this.profileImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isOnboardingComplete = false,
    this.subscriptionTier = SubscriptionTier.pro,
    this.subscriptionEndDate,
    this.dailyMessageCount = 0,
    this.dailyImageCount = 0,
    required this.lastUsageReset,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      username: json['username'] as String,
      gender: json['gender'] as String,
      profileImageUrl: json['profile_image_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isOnboardingComplete: json['is_onboarding_complete'] as bool? ?? false,
      subscriptionTier: SubscriptionTier.fromString(json['subscription_tier'] as String?),
      subscriptionEndDate: json['subscription_end_date'] != null
          ? DateTime.parse(json['subscription_end_date'] as String)
          : null,
      dailyMessageCount: json['daily_message_count'] as int? ?? 0,
      dailyImageCount: json['daily_image_count'] as int? ?? 0,
      lastUsageReset: json['last_usage_reset'] != null
          ? DateTime.parse(json['last_usage_reset'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'gender': gender,
      'profile_image_url': profileImageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_onboarding_complete': isOnboardingComplete,
      'subscription_tier': subscriptionTier.name,
      'subscription_end_date': subscriptionEndDate?.toIso8601String(),
      'daily_message_count': dailyMessageCount,
      'daily_image_count': dailyImageCount,
      'last_usage_reset': lastUsageReset.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? gender,
    String? profileImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isOnboardingComplete,
    SubscriptionTier? subscriptionTier,
    DateTime? subscriptionEndDate,
    int? dailyMessageCount,
    int? dailyImageCount,
    DateTime? lastUsageReset,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      gender: gender ?? this.gender,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isOnboardingComplete: isOnboardingComplete ?? this.isOnboardingComplete,
      subscriptionTier: subscriptionTier ?? this.subscriptionTier,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      dailyMessageCount: dailyMessageCount ?? this.dailyMessageCount,
      dailyImageCount: dailyImageCount ?? this.dailyImageCount,
      lastUsageReset: lastUsageReset ?? this.lastUsageReset,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        username,
        gender,
        profileImageUrl,
        createdAt,
        updatedAt,
        isOnboardingComplete,
        subscriptionTier,
        subscriptionEndDate,
        dailyMessageCount,
        dailyImageCount,
        lastUsageReset,
      ];
}
