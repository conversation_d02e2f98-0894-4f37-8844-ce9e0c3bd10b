import 'package:equatable/equatable.dart';

enum SubscriptionTier {
  free,
  plus,
  pro,
  elite;

  String get displayName {
    switch (this) {
      case SubscriptionTier.free:
        return 'FREE';
      case SubscriptionTier.plus:
        return 'Plus';
      case SubscriptionTier.pro:
        return 'Pro';
      case SubscriptionTier.elite:
        return 'Elite';
    }
  }

  String get description {
    switch (this) {
      case SubscriptionTier.free:
        return 'Non-Subscriber';
      case SubscriptionTier.plus:
        return '\$4.99 USD / \$7.65 AUD';
      case SubscriptionTier.pro:
        return '\$9.99 USD / \$15.31 AUD';
      case SubscriptionTier.elite:
        return '\$14.99 USD / \$22.98 AUD';
    }
  }

  String get emoji {
    switch (this) {
      case SubscriptionTier.free:
        return '🏅';
      case SubscriptionTier.plus:
        return '🥉';
      case SubscriptionTier.pro:
        return '🥈';
      case SubscriptionTier.elite:
        return '🥇';
    }
  }

  static SubscriptionTier fromString(String? value) {
    if (value == null) return SubscriptionTier.free;

    switch (value.toUpperCase()) {
      case 'PLUS':
        return SubscriptionTier.plus;
      case 'PRO':
        return SubscriptionTier.pro;
      case 'ELITE':
        return SubscriptionTier.elite;
      default:
        return SubscriptionTier.free;
    }
  }
}

class SubscriptionLimits extends Equatable {
  final int messagesPerDay;
  final int companionLimit; // -1 means unlimited
  final int imagesPerDay; // -1 means unlimited
  final bool hasMemory;
  final List<String> allowedScenarios;
  final List<String> allowedGames;
  final bool hasProfilePic;

  const SubscriptionLimits({
    required this.messagesPerDay,
    required this.companionLimit,
    required this.imagesPerDay,
    required this.hasMemory,
    required this.allowedScenarios,
    required this.allowedGames,
    required this.hasProfilePic,
  });

  static const Map<SubscriptionTier, SubscriptionLimits> tierLimits = {
    SubscriptionTier.free: SubscriptionLimits(
      messagesPerDay: 20,
      companionLimit: 1,
      imagesPerDay: 0,
      hasMemory: false,
      allowedScenarios: [],
      allowedGames: [],
      hasProfilePic: false,
    ),
    SubscriptionTier.plus: SubscriptionLimits(
      messagesPerDay: 100,
      companionLimit: 2, // 2 companions for Plus tier
      imagesPerDay: 0,
      hasMemory: true,
      allowedScenarios: ['comfort_me', 'mental_health_checkin'],
      allowedGames: [],
      hasProfilePic: false,
    ),
    SubscriptionTier.pro: SubscriptionLimits(
      messagesPerDay: 300,
      companionLimit: 4, // 4 companions for Pro tier
      imagesPerDay: 1,
      hasMemory: true,
      allowedScenarios: [
        'comfort_me',
        'mental_health_checkin',
        'i_missed_you',
        'netflix_chill',
        'romantic_dinner',
        'nsfw'
      ],
      allowedGames: ['truth_dare', 'would_rather'],
      hasProfilePic: true,
    ),
    SubscriptionTier.elite: SubscriptionLimits(
      messagesPerDay: -1, // unlimited
      companionLimit: -1, // unlimited
      imagesPerDay: -1, // unlimited
      hasMemory: true,
      allowedScenarios: [
        'comfort_me',
        'mental_health_checkin',
        'i_missed_you',
        'netflix_chill',
        'romantic_dinner',
        'nsfw'
      ],
      allowedGames: ['truth_dare', 'would_rather'],
      hasProfilePic: true,
    ),
  };

  static SubscriptionLimits getLimits(SubscriptionTier tier) {
    return tierLimits[tier] ?? tierLimits[SubscriptionTier.free]!;
  }

  bool get hasUnlimitedMessages => messagesPerDay == -1;
  bool get hasUnlimitedCompanions => companionLimit == -1;
  bool get hasUnlimitedImages => imagesPerDay == -1;

  @override
  List<Object?> get props => [
        messagesPerDay,
        companionLimit,
        imagesPerDay,
        hasMemory,
        allowedScenarios,
        allowedGames,
        hasProfilePic,
      ];
}

class SubscriptionPlan extends Equatable {
  final String id;
  final SubscriptionTier tier;
  final String name;
  final String price;
  final String period;
  final String? savings;
  final bool isRecommended;
  final List<String> features;

  const SubscriptionPlan({
    required this.id,
    required this.tier,
    required this.name,
    required this.price,
    required this.period,
    this.savings,
    this.isRecommended = false,
    required this.features,
  });

  @override
  List<Object?> get props => [id, tier, name, price, period, savings, isRecommended, features];
}
