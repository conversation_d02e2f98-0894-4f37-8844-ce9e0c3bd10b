import 'package:equatable/equatable.dart';

class MessageModel extends Equatable {
  final String id;
  final String conversationId;
  final String content;
  final String sender; // 'human' or 'ai'
  final DateTime timestamp;
  final bool isSaved;
  final String? scenarioId;
  final String? gameId;
  final String? imageUrl;
  final String? companionId;
  final String? companionName;

  const MessageModel({
    required this.id,
    required this.conversationId,
    required this.content,
    required this.sender,
    required this.timestamp,
    this.isSaved = false,
    this.scenarioId,
    this.gameId,
    this.imageUrl,
    this.companionId,
    this.companionName,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    // Handle both 'timestamp' and 'datetime' fields for backward compatibility
    DateTime timestamp;
    if (json['datetime'] != null) {
      // Handle Firestore Timestamp or DateTime from backend
      final datetimeValue = json['datetime'];
      if (datetimeValue is String) {
        timestamp = DateTime.parse(datetimeValue);
      } else if (datetimeValue.runtimeType.toString().contains('Timestamp')) {
        // Firestore Timestamp
        timestamp = datetimeValue.toDate();
      } else {
        timestamp = DateTime.now();
      }
    } else if (json['timestamp'] != null) {
      timestamp = DateTime.parse(json['timestamp'] as String);
    } else {
      timestamp = DateTime.now();
    }

    return MessageModel(
      id: json['id'] as String,
      conversationId: json['conversation_id'] as String,
      content: json['content'] as String? ?? '',
      sender: json['sender'] as String,
      timestamp: timestamp,
      isSaved: json['is_saved'] as bool? ?? false,
      scenarioId: json['scenario_id'] as String?,
      gameId: json['game_id'] as String?,
      imageUrl: json['image_url'] as String?,
      companionId: json['companion_id'] as String?,
      companionName: json['companion_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'content': content,
      'sender': sender,
      'timestamp': timestamp.toIso8601String(),
      'is_saved': isSaved,
      'scenario_id': scenarioId,
      'game_id': gameId,
      'image_url': imageUrl,
      'companion_id': companionId,
      'companion_name': companionName,
    };
  }

  MessageModel copyWith({
    String? id,
    String? conversationId,
    String? content,
    String? sender,
    DateTime? timestamp,
    bool? isSaved,
    String? scenarioId,
    String? gameId,
    String? imageUrl,
    String? companionId,
    String? companionName,
  }) {
    return MessageModel(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      content: content ?? this.content,
      sender: sender ?? this.sender,
      timestamp: timestamp ?? this.timestamp,
      isSaved: isSaved ?? this.isSaved,
      scenarioId: scenarioId ?? this.scenarioId,
      gameId: gameId ?? this.gameId,
      imageUrl: imageUrl ?? this.imageUrl,
      companionId: companionId ?? this.companionId,
      companionName: companionName ?? this.companionName,
    );
  }

  bool get isFromUser => sender == 'human';
  bool get isFromAI => sender == 'ai';
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        conversationId,
        content,
        sender,
        timestamp,
        isSaved,
        scenarioId,
        gameId,
        imageUrl,
        companionId,
        companionName,
      ];
}

class ConversationModel extends Equatable {
  final String id;
  final String userId;
  final String companionId;
  final List<MessageModel> messages;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ConversationModel({
    required this.id,
    required this.userId,
    required this.companionId,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      companionId: json['companion_id'] as String,
      messages: (json['messages'] as List<dynamic>?)
              ?.map((m) => MessageModel.fromJson(m as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'companion_id': companionId,
      'messages': messages.map((m) => m.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ConversationModel copyWith({
    String? id,
    String? userId,
    String? companionId,
    List<MessageModel>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      companionId: companionId ?? this.companionId,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  MessageModel? get lastMessage => messages.isNotEmpty ? messages.last : null;

  @override
  List<Object?> get props => [
        id,
        userId,
        companionId,
        messages,
        createdAt,
        updatedAt,
      ];
}
