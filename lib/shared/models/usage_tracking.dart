import 'package:equatable/equatable.dart';

class DailyUsage extends Equatable {
  final String userId;
  final DateTime date;
  final int messagesSent;
  final int imagesSent;
  final DateTime lastReset;

  const DailyUsage({
    required this.userId,
    required this.date,
    required this.messagesSent,
    required this.imagesSent,
    required this.lastReset,
  });

  factory DailyUsage.fromJson(Map<String, dynamic> json) {
    return DailyUsage(
      userId: json['user_id'] as String,
      date: DateTime.parse(json['date'] as String),
      messagesSent: json['messages_sent'] as int? ?? 0,
      imagesSent: json['images_sent'] as int? ?? 0,
      lastReset: DateTime.parse(json['last_reset'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'date': date.toIso8601String(),
      'messages_sent': messagesSent,
      'images_sent': imagesSent,
      'last_reset': lastReset.toIso8601String(),
    };
  }

  DailyUsage copyWith({
    String? userId,
    DateTime? date,
    int? messagesSent,
    int? imagesSent,
    DateTime? lastReset,
  }) {
    return DailyUsage(
      userId: userId ?? this.userId,
      date: date ?? this.date,
      messagesSent: messagesSent ?? this.messagesSent,
      imagesSent: imagesSent ?? this.imagesSent,
      lastReset: lastReset ?? this.lastReset,
    );
  }

  bool needsReset() {
    final now = DateTime.now();
    final resetDate = DateTime(lastReset.year, lastReset.month, lastReset.day);
    final currentDate = DateTime(now.year, now.month, now.day);
    return currentDate.isAfter(resetDate);
  }

  DailyUsage reset() {
    final now = DateTime.now();
    return copyWith(
      date: now,
      messagesSent: 0,
      imagesSent: 0,
      lastReset: now,
    );
  }

  @override
  List<Object?> get props => [userId, date, messagesSent, imagesSent, lastReset];
}

class UsageStatus extends Equatable {
  final bool canSendMessage;
  final bool canSendImage;
  final bool canCreateCompanion;
  final int remainingMessages;
  final int remainingImages;
  final String? limitMessage;

  const UsageStatus({
    required this.canSendMessage,
    required this.canSendImage,
    required this.canCreateCompanion,
    required this.remainingMessages,
    required this.remainingImages,
    this.limitMessage,
  });

  @override
  List<Object?> get props => [
        canSendMessage,
        canSendImage,
        canCreateCompanion,
        remainingMessages,
        remainingImages,
        limitMessage,
      ];
}
