import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CompanionModel extends Equatable {
  final String id;
  final String userId;
  final String name;
  final String gender;
  final String connectionType; // romantic or friendship
  final String personalityStyle; // normal or wildcard
  final String? imageUrl;
  final String conversationId;
  final int relationshipLevel; // 0-7 (Stranger to Soulmate)
  final String relationshipStatus; // Friendly, Cold, Hot, etc.
  final int relationshipScore; // 0-100
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastInteraction;

  const CompanionModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.gender,
    required this.connectionType,
    required this.personalityStyle,
    this.imageUrl,
    required this.conversationId,
    this.relationshipLevel = 0,
    this.relationshipStatus = 'Friendly',
    this.relationshipScore = 0,
    this.isActive = false,
    required this.createdAt,
    required this.updatedAt,
    this.lastInteraction,
  });

  factory CompanionModel.fromJson(Map<String, dynamic> json) {
    return CompanionModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      gender: json['gender'] as String,
      connectionType: json['connection_type'] as String,
      personalityStyle: json['personality_style'] as String,
      imageUrl: json['image_url'] as String?,
      conversationId: json['conversation_id'] as String,
      relationshipLevel: json['relationship_level'] as int? ?? 0,
      relationshipStatus: json['relationship_status'] as String? ?? 'Friendly',
      relationshipScore: json['relationship_score'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? false,
      createdAt: _parseDateTime(json['created_at']),
      updatedAt: _parseDateTime(json['updated_at']),
      lastInteraction: json['last_interaction'] != null
          ? _parseDateTime(json['last_interaction'])
          : null,
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    if (value is Timestamp) {
      return value.toDate();
    } else if (value is String) {
      return DateTime.parse(value);
    } else {
      return DateTime.now();
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'gender': gender,
      'connection_type': connectionType,
      'personality_style': personalityStyle,
      'image_url': imageUrl,
      'conversation_id': conversationId,
      'relationship_level': relationshipLevel,
      'relationship_status': relationshipStatus,
      'relationship_score': relationshipScore,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_interaction': lastInteraction?.toIso8601String(),
    };
  }

  CompanionModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? gender,
    String? connectionType,
    String? personalityStyle,
    String? imageUrl,
    String? conversationId,
    int? relationshipLevel,
    String? relationshipStatus,
    int? relationshipScore,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastInteraction,
  }) {
    return CompanionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      connectionType: connectionType ?? this.connectionType,
      personalityStyle: personalityStyle ?? this.personalityStyle,
      imageUrl: imageUrl ?? this.imageUrl,
      conversationId: conversationId ?? this.conversationId,
      relationshipLevel: relationshipLevel ?? this.relationshipLevel,
      relationshipStatus: relationshipStatus ?? this.relationshipStatus,
      relationshipScore: relationshipScore ?? this.relationshipScore,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastInteraction: lastInteraction ?? this.lastInteraction,
    );
  }

  String get relationshipLevelName {
    const levels = [
      'Stranger',
      'Acquaintance',
      'Friend',
      'Close Friend',
      'Best Friend',
      'Romantic Interest',
      'Partner',
      'Soulmate',
    ];
    return levels[relationshipLevel.clamp(0, levels.length - 1)];
  }

  String get relationshipDescription {
    if (relationshipScore <= 20) {
      return 'Your relationship is still developing. Keep chatting to build a stronger connection.';
    } else if (relationshipScore <= 40) {
      return 'You\'re getting to know each other better. The conversation is becoming more comfortable.';
    } else if (relationshipScore <= 60) {
      return 'You have a good friendship developing. There\'s mutual trust and understanding.';
    } else if (relationshipScore <= 80) {
      return 'You share a strong bond with deep conversations and emotional support.';
    } else {
      return 'You have an incredibly deep connection with profound trust and understanding.';
    }
  }

  Color get relationshipStatusColor {
    switch (relationshipStatus.toLowerCase()) {
      case 'cold':
      case 'distant':
      case 'formal':
        return const Color(0xFF4FC3F7); // Blue
      case 'friendly':
      case 'warm':
      case 'comfortable':
        return const Color(0xFF66BB6A); // Green
      case 'flirty':
      case 'playful':
      case 'romantic':
        return const Color(0xFFEC407A); // Pink
      case 'passionate':
      case 'intimate':
      case 'devoted':
        return const Color(0xFFE57373); // Red
      default:
        return const Color(0xFF42A5F5); // Default blue
    }
  }

  String get connectionTypeDisplayName {
    return connectionType == 'romantic' ? 'Romantic' : 'Friendship';
  }

  String get personalityDisplayName {
    return personalityStyle == 'normal' ? 'Normal' : 'Wildcard';
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        gender,
        connectionType,
        personalityStyle,
        imageUrl,
        conversationId,
        relationshipLevel,
        relationshipStatus,
        relationshipScore,
        isActive,
        createdAt,
        updatedAt,
        lastInteraction,
      ];
}
