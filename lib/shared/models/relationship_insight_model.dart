import 'package:equatable/equatable.dart';

class RelationshipInsightModel extends Equatable {
  final String title;
  final String description;
  final String category;

  const RelationshipInsightModel({
    required this.title,
    required this.description,
    required this.category,
  });

  factory RelationshipInsightModel.fromJson(Map<String, dynamic> json) {
    return RelationshipInsightModel(
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'category': category,
    };
  }

  RelationshipInsightModel copyWith({
    String? title,
    String? description,
    String? category,
  }) {
    return RelationshipInsightModel(
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }

  @override
  List<Object?> get props => [title, description, category];

  @override
  String toString() {
    return 'RelationshipInsightModel(title: $title, description: $description, category: $category)';
  }
}

class RelationshipInsightsResponse extends Equatable {
  final int finalScore;
  final String relationshipDescription;
  final List<RelationshipInsightModel> insights;

  const RelationshipInsightsResponse({
    required this.finalScore,
    required this.relationshipDescription,
    required this.insights,
  });

  factory RelationshipInsightsResponse.fromJson(Map<String, dynamic> json) {
    return RelationshipInsightsResponse(
      finalScore: json['final_score'] as int,
      relationshipDescription: json['relationship_description'] as String,
      insights: (json['insights'] as List<dynamic>)
          .map((insight) => RelationshipInsightModel.fromJson(insight as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'final_score': finalScore,
      'relationship_description': relationshipDescription,
      'insights': insights.map((insight) => insight.toJson()).toList(),
    };
  }

  RelationshipInsightsResponse copyWith({
    int? finalScore,
    String? relationshipDescription,
    List<RelationshipInsightModel>? insights,
  }) {
    return RelationshipInsightsResponse(
      finalScore: finalScore ?? this.finalScore,
      relationshipDescription: relationshipDescription ?? this.relationshipDescription,
      insights: insights ?? this.insights,
    );
  }

  @override
  List<Object?> get props => [finalScore, relationshipDescription, insights];

  @override
  String toString() {
    return 'RelationshipInsightsResponse(finalScore: $finalScore, relationshipDescription: $relationshipDescription, insights: $insights)';
  }
}
