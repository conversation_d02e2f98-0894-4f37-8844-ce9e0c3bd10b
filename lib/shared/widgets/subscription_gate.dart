import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/premium/presentation/bloc/premium_bloc.dart';
import '../../features/premium/presentation/bloc/premium_state.dart';
import '../../features/premium/presentation/pages/premium_page.dart';
import '../../core/theme/app_theme.dart';
import '../models/subscription_tier.dart';

class SubscriptionGate extends StatelessWidget {
  final Widget child;
  final SubscriptionTier requiredTier;
  final String? featureId;
  final String? scenarioId;
  final String? gameId;
  final String? customMessage;
  final VoidCallback? onUpgradePressed;

  const SubscriptionGate({
    super.key,
    required this.child,
    required this.requiredTier,
    this.featureId,
    this.scenarioId,
    this.gameId,
    this.customMessage,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PremiumBloc, PremiumState>(
      builder: (context, state) {
        // Check if user has required tier
        if (state.currentTier.index >= requiredTier.index) {
          return child;
        }

        // Show upgrade prompt
        return _buildUpgradePrompt(context, state);
      },
    );
  }

  Widget _buildUpgradePrompt(BuildContext context, PremiumState state) {
    final message = customMessage ?? _getDefaultMessage();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getTierColor(requiredTier).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock,
            color: _getTierColor(requiredTier),
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            '${requiredTier.emoji} ${requiredTier.displayName} Feature',
            style: const TextStyle(
              color: AppTheme.primaryTextColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: AppTheme.secondaryTextColor,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onUpgradePressed ?? () => _showUpgradeDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getTierColor(requiredTier),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Upgrade to ${requiredTier.displayName}',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDefaultMessage() {
    if (scenarioId != null) {
      return 'This scenario is available for ${requiredTier.displayName} subscribers and above.';
    } else if (gameId != null) {
      return 'This game is available for ${requiredTier.displayName} subscribers and above.';
    } else if (featureId != null) {
      switch (featureId) {
        case 'memory':
          return 'AI memory is available for ${requiredTier.displayName} subscribers and above.';
        case 'profile_pic':
          return 'Custom profile pictures are available for ${requiredTier.displayName} subscribers and above.';
        case 'unlimited_messages':
          return 'Unlimited messages are available for ${requiredTier.displayName} subscribers and above.';
        case 'unlimited_images':
          return 'Unlimited image sending is available for ${requiredTier.displayName} subscribers and above.';
        default:
          return 'This feature is available for ${requiredTier.displayName} subscribers and above.';
      }
    }
    return 'This feature is available for ${requiredTier.displayName} subscribers and above.';
  }

  Color _getTierColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return Colors.grey;
      case SubscriptionTier.plus:
        return const Color(0xFFCD7F32); // Bronze
      case SubscriptionTier.pro:
        return const Color(0xFFC0C0C0); // Silver
      case SubscriptionTier.elite:
        return const Color(0xFFFFD700); // Gold
    }
  }

  void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: Text(
          'Upgrade Required',
          style: const TextStyle(
            color: AppTheme.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          _getDefaultMessage(),
          style: const TextStyle(
            color: AppTheme.secondaryTextColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PremiumPage(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _getTierColor(requiredTier),
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}

class UsageLimitWidget extends StatelessWidget {
  final String limitType;
  final int remaining;
  final int total;
  final VoidCallback? onUpgradePressed;

  const UsageLimitWidget({
    super.key,
    required this.limitType,
    required this.remaining,
    required this.total,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context) {
    if (remaining == -1) return const SizedBox.shrink(); // Unlimited

    final percentage = total > 0 ? remaining / total : 0.0;
    final isLow = percentage < 0.2;
    final isEmpty = remaining <= 0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isEmpty ? Colors.red.withValues(alpha: 0.1) :
               isLow ? Colors.orange.withValues(alpha: 0.1) :
               Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEmpty ? Colors.red.withValues(alpha: 0.3) :
                 isLow ? Colors.orange.withValues(alpha: 0.3) :
                 Theme.of(context).colorScheme.surface,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getIconForLimitType(limitType),
            color: isEmpty ? Colors.red : 
                   isLow ? Colors.orange : 
                   AppTheme.secondaryTextColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$limitType: $remaining/$total remaining',
                  style: TextStyle(
                    color: isEmpty ? Colors.red : AppTheme.primaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: Colors.grey.withValues(alpha: 0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isEmpty ? Colors.red : 
                    isLow ? Colors.orange : 
                    AppTheme.userMessageColor,
                  ),
                ),
              ],
            ),
          ),
          if (isEmpty || isLow) ...[
            const SizedBox(width: 12),
            TextButton(
              onPressed: onUpgradePressed ?? () => _showUpgradeDialog(context),
              child: const Text(
                'Upgrade',
                style: TextStyle(
                  color: AppTheme.userMessageColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getIconForLimitType(String type) {
    switch (type.toLowerCase()) {
      case 'messages':
        return Icons.chat_bubble_outline;
      case 'images':
        return Icons.image_outlined;
      case 'companions':
        return Icons.person_outline;
      default:
        return Icons.info_outline;
    }
  }

  void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text(
          'Upgrade for More',
          style: TextStyle(
            color: AppTheme.primaryTextColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'You\'ve reached your daily $limitType limit. Upgrade to get more!',
          style: const TextStyle(
            color: AppTheme.secondaryTextColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PremiumPage(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.userMessageColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}
