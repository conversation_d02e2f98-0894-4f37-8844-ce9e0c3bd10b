import 'package:flutter/material.dart';

class CompanionAvatar extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final Color? backgroundColor;
  final Color? iconColor;
  final IconData? fallbackIcon;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const CompanionAvatar({
    super.key,
    this.imageUrl,
    this.size = 40,
    this.backgroundColor,
    this.iconColor,
    this.fallbackIcon,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        border: showBorder
            ? Border.all(
                color: borderColor ?? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                width: borderWidth,
              )
            : null,
      ),
      child: imageUrl != null && imageUrl!.isNotEmpty
          ? ClipOval(
              child: Image.network(
                imageUrl!,
                width: size,
                height: size,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildFallbackIcon(context);
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: SizedBox(
                      width: size * 0.4,
                      height: size * 0.4,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: iconColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  );
                },
              ),
            )
          : _buildFallbackIcon(context),
    );
  }

  Widget _buildFallbackIcon(BuildContext context) {
    return Icon(
      fallbackIcon ?? Icons.psychology,
      size: size * 0.6,
      color: iconColor ?? Theme.of(context).colorScheme.primary,
    );
  }
}

/// Specialized companion avatar for different use cases
class CompanionAvatarLarge extends StatelessWidget {
  final String? imageUrl;
  final VoidCallback? onTap;

  const CompanionAvatarLarge({
    super.key,
    this.imageUrl,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CompanionAvatar(
        imageUrl: imageUrl,
        size: 80,
        showBorder: true,
        borderColor: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withValues(alpha: 0.9)
            : Colors.white.withValues(alpha: 0.9),
        borderWidth: 3,
        iconColor: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.8),
      ),
    );
  }
}

/// Small companion avatar for lists and compact displays
class CompanionAvatarSmall extends StatelessWidget {
  final String? imageUrl;
  final bool isActive;

  const CompanionAvatarSmall({
    super.key,
    this.imageUrl,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CompanionAvatar(
          imageUrl: imageUrl,
          size: 32,
          showBorder: isActive,
          borderColor: Theme.of(context).colorScheme.primary,
          borderWidth: 2,
        ),
        if (isActive)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
      ],
    );
  }
}

/// Medium companion avatar for chat headers and selection
class CompanionAvatarMedium extends StatelessWidget {
  final String? imageUrl;
  final bool showOnlineIndicator;
  final VoidCallback? onTap;

  const CompanionAvatarMedium({
    super.key,
    this.imageUrl,
    this.showOnlineIndicator = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          CompanionAvatar(
            imageUrl: imageUrl,
            size: 48,
            showBorder: true,
            borderColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
          if (showOnlineIndicator)
            Positioned(
              bottom: 2,
              right: 2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).colorScheme.primary,
                  border: Border.all(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
