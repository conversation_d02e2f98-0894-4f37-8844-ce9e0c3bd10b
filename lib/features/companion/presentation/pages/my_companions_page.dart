import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/companion_model.dart';
import '../bloc/companion_bloc.dart';
import '../bloc/companion_event.dart';
import '../bloc/companion_state.dart';
import 'companion_setup_page.dart';

class MyCompanionsPage extends StatefulWidget {
  const MyCompanionsPage({super.key});

  @override
  State<MyCompanionsPage> createState() => _MyCompanionsPageState();
}

class _MyCompanionsPageState extends State<MyCompanionsPage> {
  @override
  void initState() {
    super.initState();
    // Load companions when page opens
    context.read<CompanionBloc>().add(CompanionsLoaded());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'My Companions',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocBuilder<CompanionBloc, CompanionState>(
        builder: (context, state) {
          if (state.isLoading) {
            return Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            );
          }

          if (state.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load companions',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<CompanionBloc>().add(CompanionsLoaded());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Create New Companion Button
              Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final bloc = context.read<CompanionBloc>();
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CompanionSetupPage(isFromSettings: true),
                        ),
                      );

                      // Reload companions if a new one was created
                      if (result == true && mounted) {
                        bloc.add(CompanionsLoaded());
                      }
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Create New Companion'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),
              
              // Companions List
              Expanded(
                child: state.companions.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: state.companions.length,
                        itemBuilder: (context, index) {
                          final companion = state.companions[index];
                          return _buildCompanionCard(companion);
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 80,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Companions Yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first AI companion to start chatting',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCompanionCard(CompanionModel companion) {
    return Card(
      elevation:0,
      margin: const EdgeInsets.only(bottom: 12),
      color: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Companion Avatar
              CircleAvatar(
                radius: 30,
                backgroundColor: Theme.of(context).colorScheme.primary,
                backgroundImage: companion.imageUrl != null
                    ? NetworkImage(companion.imageUrl!)
                    : null,
                child: companion.imageUrl == null
                    ? Icon(
                        companion.gender == 'male' ? Icons.man : Icons.woman,
                        size: 32,
                        color: Theme.of(context).colorScheme.onPrimary,
                      )
                    : null,
              ),
              
              const SizedBox(width: 16),
              
              // Companion Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      companion.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${companion.connectionType.toUpperCase()} • ${companion.personalityStyle.toUpperCase()}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.favorite,
                          size: 16,
                          color: companion.connectionType == 'romantic'
                              ? AppTheme.romanticColor
                              : AppTheme.friendlyColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Level ${companion.relationshipLevel}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                        const Spacer(),
                        if (companion.isActive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.successColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Active',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.successColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action Buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Edit Button
                  IconButton(
                    onPressed: () async {
                      final bloc = context.read<CompanionBloc>();
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => CompanionSetupPage(
                            companion: companion,
                            isFromSettings: true,
                          ),
                        ),
                      );

                      // Reload companions if changes were made
                      if (result == true && mounted) {
                        bloc.add(CompanionsLoaded());
                      }
                    },
                    icon: Icon(
                      Icons.edit,
                      color: AppTheme.secondaryTextColor,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),

                  // Delete Button
                  IconButton(
                    onPressed: () => _showDeleteCompanionDialog(context, companion),
                    icon: Icon(
                      Icons.delete_outline,
                      color: AppTheme.errorColor,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteCompanionDialog(BuildContext context, CompanionModel companion) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: Text(
          'Delete Companion',
          style: TextStyle(
            color: AppTheme.errorColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete ${companion.name}? This action cannot be undone and will delete all conversations and messages.',
          style: const TextStyle(color: AppTheme.secondaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.secondaryTextColor),
            ),
          ),
          BlocConsumer<CompanionBloc, CompanionState>(
            listener: (context, state) {
              if (state.errorMessage != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage!),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              } else if (!state.isLoading && state.errorMessage == null) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${companion.name} deleted successfully'),
                    backgroundColor: AppTheme.successColor,
                  ),
                );
              }
            },
            builder: (context, state) {
              return TextButton(
                onPressed: state.isLoading ? null : () {
                  context.read<CompanionBloc>().add(CompanionDeleted(companion.id));
                },
                child: state.isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.errorColor,
                          ),
                        ),
                      )
                    : const Text(
                        'Delete',
                        style: TextStyle(color: AppTheme.errorColor),
                      ),
              );
            },
          ),
        ],
      ),
    );
  }
}
