import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/companion_model.dart';
import '../bloc/companion_bloc.dart';
import '../bloc/companion_event.dart';
import '../bloc/companion_state.dart';
import '../widgets/companion_image_picker.dart';
import '../widgets/connection_type_selector.dart';
import '../widgets/personality_selector.dart';
import '../../../profile/presentation/widgets/gender_selection_widget.dart';

class CompanionSetupPage extends StatefulWidget {
  final CompanionModel? companion; // null for create mode, companion for edit mode
  final bool isOnboarding; // true if this is part of onboarding flow
  final bool isFromSettings; // true if navigated from settings/my companions

  const CompanionSetupPage({
    super.key,
    this.companion,
    this.isOnboarding = false,
    this.isFromSettings = false,
  });

  @override
  State<CompanionSetupPage> createState() => _CompanionSetupPageState();
}

class _CompanionSetupPageState extends State<CompanionSetupPage> {
  late TextEditingController _nameController;
  String _selectedGender = '';
  String _selectedConnectionType = '';
  String _selectedPersonality = '';
  String? _imageUrl;
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _isEditMode = widget.companion != null;

    // Initialize with existing data if in edit mode
    if (_isEditMode) {
      final companion = widget.companion!;
      _nameController.text = companion.name;
      _selectedGender = companion.gender;
      _selectedConnectionType = companion.connectionType;
      _selectedPersonality = companion.personalityStyle;
      _imageUrl = companion.imageUrl;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<CompanionBloc, CompanionState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }

          // Handle successful companion creation
          if (state.isSubmitted && state.companions.isNotEmpty) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(_isEditMode
                    ? 'Companion updated successfully!'
                    : 'Companion created successfully!'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );

            // Handle navigation based on context
            if (widget.isOnboarding) {
              // For onboarding flow, don't pop - let main app handle navigation
              // The main app BlocBuilder will detect the new companion and navigate to HomePage
            } else {
              // For all other contexts, pop back to previous page
              Navigator.of(context).pop(true);
            }
          }
        },
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: widget.isOnboarding ? null : AppBar(
            title: Text(
              _isEditMode ? 'Edit Companion' : 'Create Companion',
              style: TextStyle(
                color: Theme.of(context).appBarTheme.foregroundColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: Theme.of(context).appBarTheme.foregroundColor),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).scaffoldBackgroundColor,
                  Theme.of(context).scaffoldBackgroundColor.withOpacity(0.8),
                ],
              ),
            ),
            child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header - Only show when from settings/editing
                  if (widget.isFromSettings || _isEditMode) ...[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Design your perfect companion',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Update your companion\'s details and preferences',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                  ],
                  
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Welcome Text - Only show when not from settings/onboarding
                          if (!widget.isFromSettings && !widget.isOnboarding) ...[
                            Text(
                              'Design your perfect companion',
                              style: Theme.of(context).textTheme.displaySmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Customize every detail to create a unique AI companion that matches your preferences',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                            const SizedBox(height: 40),
                          ],
                          
                          const SizedBox(height: 40),
                          
                          // Companion Image
                          Center(
                            child: CompanionImagePicker(
                              imageUrl: _imageUrl,
                              onImageSelected: (imageUrl) {
                                setState(() {
                                  _imageUrl = imageUrl;
                                });
                              },
                            ),
                          ),
                          
                          const SizedBox(height: 40),
                          
                          // Name Field
                          Text(
                            'Companion Name',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _nameController,
                            decoration: const InputDecoration(
                              hintText: 'Enter companion name',
                              prefixIcon: Icon(Icons.person_outline),
                            ),
                            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Gender Selection
                          Text(
                            'Gender',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          GenderSelectionWidget(
                            selectedGender: _selectedGender,
                            onGenderSelected: (gender) {
                              setState(() {
                                _selectedGender = gender;
                              });
                            },
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Connection Type
                          Text(
                            'Connection Type',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ConnectionTypeSelector(
                            selectedType: _selectedConnectionType,
                            onTypeSelected: (type) {
                              setState(() {
                                _selectedConnectionType = type;
                              });
                            },
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Personality Style
                          Text(
                            'Personality Style',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 16),
                          PersonalitySelector(
                            selectedPersonality: _selectedPersonality,
                            onPersonalitySelected: (personality) {
                              setState(() {
                                _selectedPersonality = personality;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Create Button
                  BlocBuilder<CompanionBloc, CompanionState>(
                    builder: (context, state) {
                      final isValid = _nameController.text.isNotEmpty &&
                                    _selectedGender.isNotEmpty &&
                                    _selectedConnectionType.isNotEmpty &&
                                    _selectedPersonality.isNotEmpty;

                      return SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: isValid && !state.isLoading
                              ? () {
                                  if (_isEditMode) {
                                    context.read<CompanionBloc>().add(CompanionUpdated(
                                      companionId: widget.companion!.id,
                                      name: _nameController.text,
                                      gender: _selectedGender,
                                      connectionType: _selectedConnectionType,
                                      personality: _selectedPersonality,
                                      imageUrl: _imageUrl,
                                    ));
                                  } else {
                                    context.read<CompanionBloc>().add(CompanionSubmitted(
                                      name: _nameController.text,
                                      gender: _selectedGender,
                                      connectionType: _selectedConnectionType,
                                      personality: _selectedPersonality,
                                      imageUrl: _imageUrl,
                                    ));
                                  }
                                }
                              : null,
                          child: state.isLoading
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).colorScheme.onPrimary,
                                    ),
                                  ),
                                )
                              : Text(_isEditMode ? 'Update Companion' : 'Create Companion'),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }
}
