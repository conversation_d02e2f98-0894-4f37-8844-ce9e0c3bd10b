import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class ConnectionTypeSelector extends StatelessWidget {
  final String selectedType;
  final Function(String) onTypeSelected;

  const ConnectionTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final connectionTypes = [
      {
        'value': AppConstants.companionTypeRomantic,
        'label': 'Romantic',
        'description': 'Deep emotional connection with romantic undertones',
        'icon': Icons.favorite,
        'color': AppTheme.romanticColor,
      },
      {
        'value': AppConstants.companionTypeFriendship,
        'label': 'Friendship',
        'description': 'Supportive and caring platonic relationship',
        'icon': Icons.people,
        'color': AppTheme.friendlyColor,
      },
    ];

    return Column(
      children: connectionTypes.map((type) {
        final isSelected = selectedType == type['value'];
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: GestureDetector(
            onTap: () => onTypeSelected(type['value'] as String),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isSelected
                    ? (type['color'] as Color).withValues(alpha: 0.2)
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? type['color'] as Color
                      : Colors.transparent,
                  width: 2,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: (type['color'] as Color).withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ] : null,
              ),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected 
                          ? type['color'] as Color
                          : (type['color'] as Color).withOpacity(0.3),
                    ),
                    child: Icon(
                      type['icon'] as IconData,
                      color: isSelected
                          ? Colors.white
                          : type['color'] as Color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          type['label'] as String,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: isSelected
                                ? type['color'] as Color
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          type['description'] as String,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: type['color'] as Color,
                      size: 28,
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
