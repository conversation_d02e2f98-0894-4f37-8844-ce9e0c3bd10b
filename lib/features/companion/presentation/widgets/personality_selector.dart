import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

class PersonalitySelector extends StatelessWidget {
  final String selectedPersonality;
  final Function(String) onPersonalitySelected;

  const PersonalitySelector({
    super.key,
    required this.selectedPersonality,
    required this.onPersonalitySelected,
  });

  @override
  Widget build(BuildContext context) {
    final personalities = [
      {
        'value': AppConstants.personalityNormal,
        'label': 'Normal',
        'description': 'Balanced, thoughtful, and reliable personality',
        'icon': Icons.psychology,
        'color': AppTheme.successColor,
      },
      {
        'value': AppConstants.personalityWildcard,
        'label': 'Wildcard',
        'description': 'Spontaneous, unpredictable, and adventurous',
        'icon': Icons.casino,
        'color': AppTheme.warningColor,
      },
    ];

    return Column(
      children: personalities.map((personality) {
        final isSelected = selectedPersonality == personality['value'];
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: GestureDetector(
            onTap: () => onPersonalitySelected(personality['value'] as String),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isSelected
                    ? (personality['color'] as Color).withValues(alpha: 0.2)
                    : Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? personality['color'] as Color
                      : Colors.transparent,
                  width: 2,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: (personality['color'] as Color).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ] : null,
              ),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected 
                          ? personality['color'] as Color
                          : (personality['color'] as Color).withValues(alpha: 0.3),
                    ),
                    child: Icon(
                      personality['icon'] as IconData,
                      color: isSelected 
                          ? AppTheme.primaryTextColor
                          : personality['color'] as Color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          personality['label'] as String,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: isSelected
                                ? personality['color'] as Color
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          personality['description'] as String,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: personality['color'] as Color,
                      size: 28,
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
