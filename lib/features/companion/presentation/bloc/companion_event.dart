import 'package:equatable/equatable.dart';

abstract class CompanionEvent extends Equatable {
  const CompanionEvent();

  @override
  List<Object?> get props => [];
}

class CompanionNameChanged extends CompanionEvent {
  final String name;

  const CompanionNameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class CompanionGenderChanged extends CompanionEvent {
  final String gender;

  const CompanionGenderChanged(this.gender);

  @override
  List<Object?> get props => [gender];
}

class CompanionConnectionTypeChanged extends CompanionEvent {
  final String connectionType;

  const CompanionConnectionTypeChanged(this.connectionType);

  @override
  List<Object?> get props => [connectionType];
}

class CompanionPersonalityChanged extends CompanionEvent {
  final String personality;

  const CompanionPersonalityChanged(this.personality);

  @override
  List<Object?> get props => [personality];
}

class CompanionImageChanged extends CompanionEvent {
  final String? imageUrl;

  const CompanionImageChanged(this.imageUrl);

  @override
  List<Object?> get props => [imageUrl];
}

class CompanionSubmitted extends CompanionEvent {
  final String name;
  final String gender;
  final String connectionType;
  final String personality;
  final String? imageUrl;

  const CompanionSubmitted({
    required this.name,
    required this.gender,
    required this.connectionType,
    required this.personality,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [name, gender, connectionType, personality, imageUrl];
}

class CompanionUpdated extends CompanionEvent {
  final String companionId;
  final String name;
  final String gender;
  final String connectionType;
  final String personality;
  final String? imageUrl;

  const CompanionUpdated({
    required this.companionId,
    required this.name,
    required this.gender,
    required this.connectionType,
    required this.personality,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [companionId, name, gender, connectionType, personality, imageUrl];
}

class CompanionReset extends CompanionEvent {}

class CompanionsLoaded extends CompanionEvent {}

class CompanionSelected extends CompanionEvent {
  final String companionId;

  const CompanionSelected(this.companionId);

  @override
  List<Object?> get props => [companionId];
}

class CompanionDeleted extends CompanionEvent {
  final String companionId;

  const CompanionDeleted(this.companionId);

  @override
  List<Object?> get props => [companionId];
}
