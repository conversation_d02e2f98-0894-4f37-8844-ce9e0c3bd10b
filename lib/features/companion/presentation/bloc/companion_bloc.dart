import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/services/usage_tracking_service.dart';
import '../../../../shared/models/companion_model.dart';
import '../../data/companion_service.dart';
import 'companion_event.dart';
import 'companion_state.dart';

class CompanionBloc extends Bloc<CompanionEvent, CompanionState> {
  final StorageService _storageService;
  final CompanionService _companionService;
  final UsageTrackingService _usageTrackingService;

  CompanionBloc({
    required StorageService storageService,
    required FirebaseService firebaseService,
    required UsageTrackingService usageTrackingService,
  })  : _storageService = storageService,
        _companionService = CompanionService(
          firebaseService: firebaseService,
          storageService: storageService,
        ),
        _usageTrackingService = usageTrackingService,
        super(const CompanionState()) {
    on<CompanionNameChanged>(_onNameChanged);
    on<CompanionGenderChanged>(_onGenderChanged);
    on<CompanionConnectionTypeChanged>(_onConnectionTypeChanged);
    on<CompanionPersonalityChanged>(_onPersonalityChanged);
    on<CompanionImageChanged>(_onImageChanged);
    on<CompanionSubmitted>(_onSubmitted);
    on<CompanionUpdated>(_onUpdated);
    on<CompanionDeleted>(_onDeleted);
    on<CompanionReset>(_onReset);
    on<CompanionsLoaded>(_onCompanionsLoaded);
    on<CompanionSelected>(_onCompanionSelected);
  }

  void _onNameChanged(
    CompanionNameChanged event,
    Emitter<CompanionState> emit,
  ) {
    final newState = state.copyWith(
      name: event.name,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidCompanion(newState)));
  }

  void _onGenderChanged(
    CompanionGenderChanged event,
    Emitter<CompanionState> emit,
  ) {
    final newState = state.copyWith(
      gender: event.gender,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidCompanion(newState)));
  }

  void _onConnectionTypeChanged(
    CompanionConnectionTypeChanged event,
    Emitter<CompanionState> emit,
  ) {
    final newState = state.copyWith(
      connectionType: event.connectionType,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidCompanion(newState)));
  }

  void _onPersonalityChanged(
    CompanionPersonalityChanged event,
    Emitter<CompanionState> emit,
  ) {
    final newState = state.copyWith(
      personality: event.personality,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidCompanion(newState)));
  }

  void _onImageChanged(
    CompanionImageChanged event,
    Emitter<CompanionState> emit,
  ) {
    final newState = state.copyWith(
      imageUrl: event.imageUrl,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidCompanion(newState)));
  }

  Future<void> _onSubmitted(
    CompanionSubmitted event,
    Emitter<CompanionState> emit,
  ) async {
    // Check companion creation limits
    final canCreateCompanion = await _usageTrackingService.canCreateCompanion();
    if (!canCreateCompanion) {
      final usageStatus = await _usageTrackingService.getUsageStatus();
      emit(state.copyWith(
        errorMessage: usageStatus.limitMessage ?? 'Companion limit reached. Upgrade for more companions!',
      ));
      return;
    }

    // Validate the submitted data
    if (event.name.trim().isEmpty ||
        event.name.trim().length < 2 ||
        event.gender.isEmpty ||
        event.connectionType.isEmpty ||
        event.personality.isEmpty) {
      emit(state.copyWith(
        errorMessage: 'Please fill in all required fields',
      ));
      return;
    }

    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final user = _storageService.getUser();
      if (user == null) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'User not found',
        ));
        return;
      }

      // Create new companion using CompanionService with event data
      final companion = await _companionService.createCompanion(
        userId: user.id,
        name: event.name,
        gender: event.gender,
        connectionType: event.connectionType,
        personalityStyle: event.personality,
        imageUrl: event.imageUrl,
      );

      // Get updated companions list
      final companions = await _companionService.getUserCompanions(user.id);

      emit(state.copyWith(
        isLoading: false,
        isSubmitted: true,
        companions: companions,
        selectedCompanion: companion.isActive ? companion : state.selectedCompanion,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  Future<void> _onUpdated(
    CompanionUpdated event,
    Emitter<CompanionState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Update the companion
      final companion = await _companionService.updateCompanion(
        companionId: event.companionId,
        name: event.name,
        gender: event.gender,
        connectionType: event.connectionType,
        personalityStyle: event.personality,
        imageUrl: event.imageUrl,
      );

      // Update the companions list
      final updatedCompanions = state.companions.map((c) {
        return c.id == event.companionId ? companion : c;
      }).toList();

      emit(state.copyWith(
        isLoading: false,
        isSubmitted: true,
        companions: updatedCompanions,
        selectedCompanion: companion.isActive ? companion : state.selectedCompanion,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  Future<void> _onDeleted(
    CompanionDeleted event,
    Emitter<CompanionState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Delete the companion
      await _companionService.deleteCompanion(event.companionId);

      // Remove from companions list
      final updatedCompanions = state.companions
          .where((c) => c.id != event.companionId)
          .toList();

      // Clear selected companion if it was deleted
      CompanionModel? newSelectedCompanion = state.selectedCompanion;
      if (state.selectedCompanion?.id == event.companionId) {
        newSelectedCompanion = updatedCompanions.isNotEmpty ? updatedCompanions.first : null;
      }

      emit(state.copyWith(
        isLoading: false,
        companions: updatedCompanions,
        selectedCompanion: newSelectedCompanion,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  void _onReset(
    CompanionReset event,
    Emitter<CompanionState> emit,
  ) {
    emit(state.copyWith(
      name: '',
      gender: '',
      connectionType: '',
      personality: '',
      imageUrl: null,
      isValid: false,
      isLoading: false,
      errorMessage: null,
      isSubmitted: false,
    ));
  }

  Future<void> _onCompanionsLoaded(
    CompanionsLoaded event,
    Emitter<CompanionState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      final user = _storageService.getUser();

      if (user != null) {
        // Load companions from Firebase
        final companions = await _companionService.getUserCompanions(user.id);
        final activeCompanion = await _companionService.getActiveCompanion(user.id);

        emit(state.copyWith(
          isLoading: false,
          companions: companions,
          selectedCompanion: activeCompanion,
        ));
      } else {
        // Fallback to local storage
        final companions = _storageService.getCompanions();
        final selectedCompanion = _storageService.getSelectedCompanion();

        emit(state.copyWith(
          isLoading: false,
          companions: companions,
          selectedCompanion: selectedCompanion,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  Future<void> _onCompanionSelected(
    CompanionSelected event,
    Emitter<CompanionState> emit,
  ) async {
    try {
      final user = _storageService.getUser();
      if (user != null) {
        // Set active companion in Firebase
        await _companionService.setActiveCompanion(user.id, event.companionId);

        // Get updated companion
        final companion = state.companions.firstWhere(
          (c) => c.id == event.companionId,
        );

        emit(state.copyWith(selectedCompanion: companion));
      } else {
        // Fallback to local storage
        final companion = state.companions.firstWhere(
          (c) => c.id == event.companionId,
        );

        await _storageService.saveSelectedCompanion(companion);
        emit(state.copyWith(selectedCompanion: companion));
      }
    } catch (e) {
      emit(state.copyWith(
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  bool _isValidCompanion(CompanionState state) {
    return state.name.trim().isNotEmpty &&
           state.name.trim().length >= 2 &&
           state.gender.isNotEmpty &&
           state.connectionType.isNotEmpty &&
           state.personality.isNotEmpty;
  }
}
