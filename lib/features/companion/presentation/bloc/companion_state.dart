import 'package:equatable/equatable.dart';
import '../../../../shared/models/companion_model.dart';

class CompanionState extends Equatable {
  final String name;
  final String gender;
  final String connectionType;
  final String personality;
  final String? imageUrl;
  final bool isValid;
  final bool isLoading;
  final String? errorMessage;
  final bool isSubmitted;
  final List<CompanionModel> companions;
  final CompanionModel? selectedCompanion;

  const CompanionState({
    this.name = '',
    this.gender = '',
    this.connectionType = '',
    this.personality = '',
    this.imageUrl,
    this.isValid = false,
    this.isLoading = false,
    this.errorMessage,
    this.isSubmitted = false,
    this.companions = const [],
    this.selectedCompanion,
  });

  CompanionState copyWith({
    String? name,
    String? gender,
    String? connectionType,
    String? personality,
    String? imageUrl,
    bool? isValid,
    bool? isLoading,
    String? errorMessage,
    bool? isSubmitted,
    List<CompanionModel>? companions,
    CompanionModel? selectedCompanion,
  }) {
    return CompanionState(
      name: name ?? this.name,
      gender: gender ?? this.gender,
      connectionType: connectionType ?? this.connectionType,
      personality: personality ?? this.personality,
      imageUrl: imageUrl ?? this.imageUrl,
      isValid: isValid ?? this.isValid,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      companions: companions ?? this.companions,
      selectedCompanion: selectedCompanion ?? this.selectedCompanion,
    );
  }

  @override
  List<Object?> get props => [
        name,
        gender,
        connectionType,
        personality,
        imageUrl,
        isValid,
        isLoading,
        errorMessage,
        isSubmitted,
        companions,
        selectedCompanion,
      ];
}
