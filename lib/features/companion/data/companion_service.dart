import 'package:uuid/uuid.dart';
import '../../../core/services/firebase_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../shared/models/companion_model.dart';

class CompanionService {
  final FirebaseService _firebaseService;
  final StorageService _storageService;

  CompanionService({
    required FirebaseService firebaseService,
    required StorageService storageService,
  })  : _firebaseService = firebaseService,
        _storageService = storageService;

  Future<CompanionModel> createCompanion({
    required String userId,
    required String name,
    required String gender,
    required String connectionType,
    required String personalityStyle,
    String? imageUrl,
  }) async {
    try {
      final companionId = const Uuid().v4();
      final conversationId = const Uuid().v4();

      // Check if this is the user's first companion
      final existingCompanions = await _firebaseService.getUserCompanions(userId);
      final isFirstCompanion = existingCompanions.isEmpty;

      final companion = CompanionModel(
        id: companionId,
        userId: userId,
        name: name,
        gender: gender,
        connectionType: connectionType,
        personalityStyle: personalityStyle,
        imageUrl: imageUrl,
        conversationId: conversationId,
        relationshipLevel: 0, // Start as Stranger
        relationshipStatus: 'Friendly',
        relationshipScore: 0,
        isActive: isFirstCompanion, // First companion is active by default
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create companion in Firestore
      await _firebaseService.createCompanion(companion);

      // Create conversation for this companion
      await _firebaseService.createConversation(conversationId, userId, companionId);

      // If this is the first companion, set it as active
      if (isFirstCompanion) {
        await _storageService.saveSelectedCompanion(companion);
      }

      return companion;
    } catch (e) {
      throw Exception('Failed to create companion: ${e.toString()}');
    }
  }

  Future<List<CompanionModel>> getUserCompanions(String userId) async {
    try {
      final companions = await _firebaseService.getUserCompanions(userId);
      
      // Save to local storage for offline access
      await _storageService.saveCompanions(companions);
      
      return companions;
    } catch (e) {
      throw Exception('Failed to get companions: ${e.toString()}');
    }
  }

  Future<void> setActiveCompanion(String userId, String companionId) async {
    try {
      await _firebaseService.setActiveCompanion(userId, companionId);
      
      // Update local storage
      final companions = await _firebaseService.getUserCompanions(userId);
      final activeCompanion = companions.firstWhere((c) => c.id == companionId);
      await _storageService.saveSelectedCompanion(activeCompanion);
      
    } catch (e) {
      throw Exception('Failed to set active companion: ${e.toString()}');
    }
  }

  Future<CompanionModel?> getActiveCompanion(String userId) async {
    try {
      final companions = await _firebaseService.getUserCompanions(userId);
      final activeCompanion = companions.where((c) => c.isActive).firstOrNull;
      
      if (activeCompanion != null) {
        await _storageService.saveSelectedCompanion(activeCompanion);
      }
      
      return activeCompanion;
    } catch (e) {
      throw Exception('Failed to get active companion: ${e.toString()}');
    }
  }

  Future<void> updateCompanionRelationship({
    required String companionId,
    int? relationshipLevel,
    String? relationshipStatus,
    int? relationshipScore,
  }) async {
    try {
      // Get current companion data
      final companions = await _firebaseService.getUserCompanions(
        _firebaseService.currentUser!.uid,
      );
      
      final companion = companions.firstWhere((c) => c.id == companionId);
      
      final updatedCompanion = companion.copyWith(
        relationshipLevel: relationshipLevel,
        relationshipStatus: relationshipStatus,
        relationshipScore: relationshipScore,
        lastInteraction: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firebaseService.updateCompanion(updatedCompanion);
      
      // Update local storage if this is the active companion
      if (companion.isActive) {
        await _storageService.saveSelectedCompanion(updatedCompanion);
      }
    } catch (e) {
      throw Exception('Failed to update companion relationship: ${e.toString()}');
    }
  }

  Future<CompanionModel> updateCompanion({
    required String companionId,
    required String name,
    required String gender,
    required String connectionType,
    required String personalityStyle,
    String? imageUrl,
  }) async {
    try {
      final userId = _firebaseService.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get the existing companion first
      final companions = await _firebaseService.getUserCompanions(userId);
      final existingCompanion = companions.firstWhere((c) => c.id == companionId);

      // Create updated companion model
      final updatedCompanion = existingCompanion.copyWith(
        name: name,
        gender: gender,
        connectionType: connectionType,
        personalityStyle: personalityStyle,
        imageUrl: imageUrl,
        updatedAt: DateTime.now(),
      );

      // Update companion in Firebase
      await _firebaseService.updateCompanion(updatedCompanion);

      return updatedCompanion;
    } catch (e) {
      throw Exception('Failed to update companion: ${e.toString()}');
    }
  }

  Future<void> deleteCompanion(String companionId) async {
    try {
      final userId = _firebaseService.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Delete companion from Firebase
      await _firebaseService.deleteCompanion(companionId);

      // Clear from local storage if it was the selected companion
      final selectedCompanion = _storageService.getSelectedCompanion();
      if (selectedCompanion?.id == companionId) {
        await _storageService.clearSelectedCompanion();
      }
    } catch (e) {
      throw Exception('Failed to delete companion: ${e.toString()}');
    }
  }
}
