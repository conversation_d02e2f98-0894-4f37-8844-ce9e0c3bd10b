import 'package:flutter/material.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart' as apple;

class AppleSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;

  const AppleSignInButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: apple.SignInWithAppleButton(
        onPressed: onPressed ?? () {},
        style: apple.SignInWithAppleButtonStyle.black,
        borderRadius: BorderRadius.circular(12),
        text: 'Continue with Apple',
        iconAlignment: apple.IconAlignment.left,
      ),
    );
  }
}
