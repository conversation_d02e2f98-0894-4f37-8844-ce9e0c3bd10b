import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../shared/models/user_model.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final StorageService _storageService;
  final FirebaseService _firebaseService;

  AuthBloc({
    required StorageService storageService,
    required FirebaseService firebaseService,
  })  : _storageService = storageService,
        _firebaseService = firebaseService,
        super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignInWithGoogleRequested>(_onSignInWithGoogleRequested);
    on<AuthSignOutRequested>(_onSignOutRequested);
    on<AuthUserUpdated>(_onUserUpdated);
    on<AuthDeleteAccountRequested>(_onDeleteAccountRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final firebaseUser = _firebaseService.currentUser;

      if (firebaseUser != null) {
        // Check if user exists in Firestore
        final user = await _firebaseService.getUserById(firebaseUser.uid);

        if (user != null) {
          await _storageService.saveUser(user);
          emit(AuthAuthenticated(user));
        } else {
          // User exists in Firebase Auth but not in Firestore
          // This means they need to complete profile setup
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError('Failed to check authentication status'));
    }
  }

  Future<void> _onSignInWithGoogleRequested(
    AuthSignInWithGoogleRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final userCredential = await _firebaseService.signInWithGoogle();

      if (userCredential?.user != null) {
        final firebaseUser = userCredential!.user!;

        // Check if user already exists in Firestore
        UserModel? existingUser = await _firebaseService.getUserById(firebaseUser.uid);

        if (existingUser != null) {
          // User exists, sign them in
          await _storageService.saveUser(existingUser);
          emit(AuthAuthenticated(existingUser));
        } else {
          // New user, create basic user record
          final newUser = UserModel(
            id: firebaseUser.uid,
            email: firebaseUser.email ?? '',
            username: '', // Will be set during profile setup
            gender: '', // Will be set during profile setup
            profileImageUrl: firebaseUser.photoURL,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            isOnboardingComplete: false,
            lastUsageReset: DateTime.now(),
          );

          // Save to Firestore and local storage
          await _firebaseService.createUser(newUser);
          await _storageService.saveUser(newUser);

          emit(AuthAuthenticated(newUser));
        }
      } else {
        emit(AuthError('Google sign-in was cancelled'));
      }
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _onSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _firebaseService.signOut();
      await _storageService.clearUser();
      await _storageService.clearToken();
      await _storageService.clearSelectedCompanion();

      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _onUserUpdated(
    AuthUserUpdated event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      final currentUser = (state as AuthAuthenticated).user;
      final updatedUser = currentUser.copyWith(
        updatedAt: DateTime.now(),
      );
      
      await _storageService.saveUser(updatedUser);
      emit(AuthAuthenticated(updatedUser));
    }
  }

  // Real Google Sign-In implementation (commented for development)
  /*
  Future<void> _onSignInWithGoogleRequested(
    AuthSignInWithGoogleRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        emit(AuthUnauthenticated());
        return;
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // Here you would typically send the token to your backend
      // and get user data back
      
      final user = UserModel(
        id: googleUser.id,
        email: googleUser.email,
        username: googleUser.displayName ?? '',
        gender: '',
        profileImageUrl: googleUser.photoUrl,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isOnboardingComplete: false,
      );

      await _storageService.saveUser(user);
      await _storageService.saveToken(googleAuth.accessToken ?? '');

      emit(AuthAuthenticated(user));
    } catch (e) {
      emit(AuthError('Failed to sign in with Google'));
    }
  }
  */

  Future<void> _onDeleteAccountRequested(
    AuthDeleteAccountRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final currentUser = _firebaseService.currentUser;
      if (currentUser == null) {
        emit(AuthError('No user is currently signed in'));
        return;
      }

      // Delete all user data from Firestore and Firebase Auth
      await _firebaseService.deleteUserAccount(currentUser.uid);

      // Clear local storage
      await _storageService.clearAll();

      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError('Failed to delete account: ${e.toString()}'));
    }
  }
}
