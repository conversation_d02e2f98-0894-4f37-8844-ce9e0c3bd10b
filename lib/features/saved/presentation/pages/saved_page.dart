import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/error_widget.dart';
import '../../../companion/presentation/bloc/companion_bloc.dart';
import '../../../companion/presentation/bloc/companion_state.dart';
import '../bloc/saved_bloc.dart';
import '../bloc/saved_event.dart';
import '../bloc/saved_state.dart';
import '../widgets/saved_message_card.dart';
import '../widgets/companion_filter_bar.dart';

class SavedPage extends StatefulWidget {
  final String? companionId;
  final String? companionName;

  const SavedPage({
    super.key,
    this.companionId,
    this.companionName,
  });

  @override
  State<SavedPage> createState() => _SavedPageState();
}

class _SavedPageState extends State<SavedPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedCompanionFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _selectedCompanionFilter = widget.companionId; // Initialize with passed companion ID
    // Always load ALL saved messages and media, then filter in UI
    context.read<SavedBloc>().add(SavedMessagesLoaded(companionId: null));
    context.read<SavedBloc>().add(SavedMediaLoaded(companionId: null));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          widget.companionName != null
              ? 'Saved - ${widget.companionName}'
              : 'Saved',
          style: TextStyle(
            color: Theme.of(context).appBarTheme.foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          indicatorColor: Theme.of(context).colorScheme.primary,
          tabs: const [
            Tab(
              icon: Icon(Icons.chat_bubble),
              text: 'Messages',
            ),
            Tab(
              icon: Icon(Icons.photo),
              text: 'Media',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Companion Filter Bar (only show when not filtering by specific companion)
          if (widget.companionId == null)
            BlocBuilder<CompanionBloc, CompanionState>(
              builder: (context, companionState) {
                return CompanionFilterBar(
                  companions: companionState.companions,
                  selectedCompanionId: _selectedCompanionFilter,
                  onCompanionSelected: (companionId) {
                    setState(() {
                      _selectedCompanionFilter = companionId;
                    });
                    // Don't reload - just update the filter and let UI filtering handle it
                    print('SavedPage: Selected companion filter updated to: $companionId');
                  },
                );
              },
            ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSavedMessagesTab(),
                _buildSavedMediaTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedMessagesTab() {
    return BlocBuilder<SavedBloc, SavedState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
           
              Expanded(
                child: _buildMessagesList(state),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessagesList(SavedState state) {
    if (state.isLoading) {
      return const LoadingWidget(message: 'Loading saved messages...');
    }

    if (state.errorMessage != null) {
      return CustomErrorWidget(
        message: state.errorMessage!,
        onRetry: () {
          context.read<SavedBloc>().add(SavedMessagesLoaded(companionId: widget.companionId));
        },
      );
    }

    // Debug logging
    print('SavedPage: Total saved messages: ${state.savedMessages.length}');
    print('SavedPage: Selected companion filter: $_selectedCompanionFilter');
    print('SavedPage: Widget companion ID: ${widget.companionId}');

    // Filter messages based on selected companion (only when using filter bar)
    List<MessageModel> filteredMessages = state.savedMessages;
    if (widget.companionId == null && _selectedCompanionFilter != null) {
      print('SavedPage: Applying filter for companion: $_selectedCompanionFilter');
      filteredMessages = state.savedMessages
          .where((message) {
            print('SavedPage: Message companionId: ${message.companionId}');
            return message.companionId == _selectedCompanionFilter;
          })
          .toList();
      print('SavedPage: Filtered messages count: ${filteredMessages.length}');
    }

    if (filteredMessages.isEmpty) {
      print('SavedPage: No messages to display');
      return _buildEmptyMessagesState();
    }

    return ListView.builder(
      itemCount: filteredMessages.length,
      itemBuilder: (context, index) {
        final message = filteredMessages[index];
        return SavedMessageCard(message: message);
      },
    );
  }

  Widget _buildEmptyMessagesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_outline,
            size: 80,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Saved Messages',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Long press on messages in chat to save your favorite moments',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSavedMediaTab() {
    return BlocBuilder<SavedBloc, SavedState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.companionName != null
                    ? 'Media with ${widget.companionName}'
                    : 'Your Saved Media',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                widget.companionName != null
                    ? 'Photos and media shared with ${widget.companionName}'
                    : 'All photos and media from your conversations',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: _buildMediaList(state),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMediaList(SavedState state) {
    if (state.isLoadingMedia) {
      return Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    if (state.errorMessage != null) {
      return CustomErrorWidget(
        message: state.errorMessage!,
        onRetry: () {
          context.read<SavedBloc>().add(SavedMediaLoaded(companionId: widget.companionId));
        },
      );
    }

    // Debug logging for media
    print('SavedPage Media: Total saved media messages: ${state.savedMediaMessages.length}');
    print('SavedPage Media: Selected companion filter: $_selectedCompanionFilter');
    print('SavedPage Media: Widget companion ID: ${widget.companionId}');

    // Filter media messages based on selected companion (only when using filter bar)
    List<MessageModel> filteredMediaMessages = state.savedMediaMessages;
    if (widget.companionId == null && _selectedCompanionFilter != null) {
      print('SavedPage Media: Applying filter for companion: $_selectedCompanionFilter');
      filteredMediaMessages = state.savedMediaMessages
          .where((message) {
            print('SavedPage Media: Message companionId: ${message.companionId}');
            return message.companionId == _selectedCompanionFilter;
          })
          .toList();
      print('SavedPage Media: Filtered media messages count: ${filteredMediaMessages.length}');
    }

    if (filteredMediaMessages.isEmpty) {
      print('SavedPage Media: No media messages to display');
      return _buildEmptyMediaState();
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1,
      ),
      itemCount: filteredMediaMessages.length,
      itemBuilder: (context, index) {
        final message = filteredMediaMessages[index];
        return _buildMediaCard(context, message);
      },
    );
  }

  Widget _buildEmptyMediaState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 80,
            color: AppTheme.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Saved Media',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppTheme.secondaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Photos and media shared in conversations will appear here',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.hintTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMediaCard(BuildContext context, MessageModel message) {
    // Ensure we have a valid image URL
    if (message.imageUrl == null || message.imageUrl!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Icon(
            Icons.broken_image,
            color: AppTheme.secondaryTextColor,
            size: 48,
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Image
            Image.network(
              message.imageUrl!,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: AppTheme.surfaceColor,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: AppTheme.userMessageColor,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: AppTheme.surfaceColor,
                  child: const Icon(
                    Icons.broken_image,
                    color: AppTheme.secondaryTextColor,
                    size: 48,
                  ),
                );
              },
            ),

            // Overlay with message info
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (message.content.isNotEmpty)
                      Text(
                        message.content,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          message.isFromUser ? Icons.person : Icons.psychology,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          message.isFromUser ? 'You' : 'Companion',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
