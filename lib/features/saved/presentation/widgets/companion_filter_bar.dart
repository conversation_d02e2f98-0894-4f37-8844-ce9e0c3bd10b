import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/companion_model.dart';
import '../../../../shared/widgets/companion_avatar.dart';

class CompanionFilterBar extends StatelessWidget {
  final List<CompanionModel> companions;
  final String? selectedCompanionId;
  final Function(String?) onCompanionSelected;

  const CompanionFilterBar({
    super.key,
    required this.companions,
    this.selectedCompanionId,
    required this.onCompanionSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          // All Messages filter
          _buildFilterChip(
            context: context,
            label: 'All',
            isSelected: selectedCompanionId == null,
            onTap: () => onCompanionSelected(null),
            avatar: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              ),
              child: Icon(
                Icons.all_inclusive,
                size: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 12),
          
          // Individual companion filters
          ...companions.map((companion) => Padding(
            padding: const EdgeInsets.only(right: 12),
            child: _buildFilterChip(
              context: context,
              label: companion.name,
              isSelected: selectedCompanionId == companion.id,
              onTap: () => onCompanionSelected(companion.id),
              avatar: CompanionAvatarSmall(
                imageUrl: companion.imageUrl,
                isActive: selectedCompanionId == companion.id,
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required BuildContext context,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required Widget avatar,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            avatar,
            const SizedBox(width: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
