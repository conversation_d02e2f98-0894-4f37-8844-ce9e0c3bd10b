import 'package:equatable/equatable.dart';
import '../../../../shared/models/message_model.dart';

class SavedState extends Equatable {
  final List<MessageModel> savedMessages;
  final List<MessageModel> savedMediaMessages;
  final bool isLoadingMedia;
  final bool isLoading;
  final String? errorMessage;

  const SavedState({
    this.savedMessages = const [],
    this.savedMediaMessages = const [],
    this.isLoading = false,
    this.isLoadingMedia = false,
    this.errorMessage,
  });

  SavedState copyWith({
    List<MessageModel>? savedMessages,
    List<MessageModel>? savedMediaMessages,
    bool? isLoading,
    bool? isLoadingMedia,
    String? errorMessage,
  }) {
    return SavedState(
      savedMessages: savedMessages ?? this.savedMessages,
      savedMediaMessages: savedMediaMessages ?? this.savedMediaMessages,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMedia: isLoadingMedia ?? this.isLoadingMedia,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        savedMessages,
        savedMediaMessages,
        isLoading,
        isLoadingMedia,
        errorMessage,
      ];
}
