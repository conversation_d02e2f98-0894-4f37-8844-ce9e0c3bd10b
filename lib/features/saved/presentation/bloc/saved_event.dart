import 'package:equatable/equatable.dart';

abstract class SavedEvent extends Equatable {
  const SavedEvent();

  @override
  List<Object?> get props => [];
}

class SavedMessagesLoaded extends SavedEvent {
  final String? companionId;

  const SavedMessagesLoaded({this.companionId});

  @override
  List<Object?> get props => [companionId];
}

class SavedMediaLoaded extends SavedEvent {
  final String? companionId;

  const SavedMediaLoaded({this.companionId});

  @override
  List<Object?> get props => [companionId];
}

class SavedMessageRemoved extends SavedEvent {
  final String messageId;

  const SavedMessageRemoved(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

class SavedMessageCopied extends SavedEvent {
  final String messageId;

  const SavedMessageCopied(this.messageId);

  @override
  List<Object?> get props => [messageId];
}
