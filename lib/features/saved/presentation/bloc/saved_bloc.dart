import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../core/services/storage_service.dart';
import '../../../chat/data/chat_service.dart';
import 'saved_event.dart';
import 'saved_state.dart';

class SavedBloc extends Bloc<SavedEvent, SavedState> {
  final ChatService _chatService;
  final StorageService _storageService;

  SavedBloc({
    required ChatService chatService,
    required StorageService storageService,
  })  : _chatService = chatService,
        _storageService = storageService,
        super(const SavedState()) {
    on<SavedMessagesLoaded>(_onSavedMessagesLoaded);
    on<SavedMediaLoaded>(_onSavedMediaLoaded);
    on<SavedMessageRemoved>(_onSavedMessageRemoved);
    on<SavedMessageCopied>(_onSavedMessageCopied);
  }

  Future<void> _onSavedMessagesLoaded(
    SavedMessagesLoaded event,
    Emitter<SavedState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      List<MessageModel> savedMessages;

      if (event.companionId != null) {
        // Load saved messages for specific companion
        savedMessages = await _chatService.getSavedMessages(event.companionId!);
      } else {
        // Load all saved messages for the current user
        final user = _storageService.getUser();
        if (user != null) {
          savedMessages = await _chatService.getAllSavedMessages(user.id);
        } else {
          savedMessages = [];
        }
      }

      emit(state.copyWith(
        isLoading: false,
        savedMessages: savedMessages,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load saved messages',
      ));
    }
  }

  Future<void> _onSavedMediaLoaded(
    SavedMediaLoaded event,
    Emitter<SavedState> emit,
  ) async {
    emit(state.copyWith(isLoadingMedia: true, errorMessage: null));

    try {
      List<MessageModel> savedMediaMessages;

      if (event.companionId != null) {
        // Load saved media messages for specific companion
        savedMediaMessages = await _chatService.getSavedMediaMessages(event.companionId!);
      } else {
        // Load all saved media messages for the current user
        final user = _storageService.getUser();
        if (user != null) {
          final allSavedMessages = await _chatService.getAllSavedMessages(user.id);
          // Filter messages that have images
          savedMediaMessages = allSavedMessages.where((message) => message.hasImage).toList();
        } else {
          savedMediaMessages = [];
        }
      }

      emit(state.copyWith(
        isLoadingMedia: false,
        savedMediaMessages: savedMediaMessages,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingMedia: false,
        errorMessage: 'Failed to load saved media',
      ));
    }
  }

  Future<void> _onSavedMessageRemoved(
    SavedMessageRemoved event,
    Emitter<SavedState> emit,
  ) async {
    try {
      final updatedMessages = state.savedMessages
          .where((message) => message.id != event.messageId)
          .toList();

      emit(state.copyWith(savedMessages: updatedMessages));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to remove message'));
    }
  }

  Future<void> _onSavedMessageCopied(
    SavedMessageCopied event,
    Emitter<SavedState> emit,
  ) async {
    try {
      // In a real app, this would copy to clipboard
      // For now, just emit success
      emit(state.copyWith(errorMessage: null));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to copy message'));
    }
  }

}
