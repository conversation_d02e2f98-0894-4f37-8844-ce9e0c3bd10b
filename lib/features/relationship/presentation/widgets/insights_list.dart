import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../shared/models/relationship_insight_model.dart';
import '../../../relationship/data/insights_service.dart';

class InsightsList extends StatefulWidget {
  final String companionId;

  const InsightsList({
    super.key,
    required this.companionId,
  });

  @override
  State<InsightsList> createState() => _InsightsListState();
}

class _InsightsListState extends State<InsightsList> {
  late final InsightsService _insightsService;
  
  List<RelationshipInsightModel> _insights = [];
  bool _isLoading = true;
  bool _hasConversationHistory = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Initialize the service - in a real app, these would be injected via dependency injection
    _insightsService = InsightsService(
      storageService: StorageService(),
      firebaseService: FirebaseService(),
    );
    _loadInsights();
  }

  @override
  void didUpdateWidget(InsightsList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reload insights if companion ID changed
    if (oldWidget.companionId != widget.companionId) {
      print('InsightsList: Companion changed from ${oldWidget.companionId} to ${widget.companionId}');
      _loadInsights();
    }
  }

  /// Public method to refresh insights - can be called from parent widgets
  void refreshInsights() {
    print('InsightsList: Manual refresh requested');
    _loadInsights();
  }

  Future<void> _loadInsights() async {
    try {
      print('InsightsList: Starting to load insights for companion ${widget.companionId}');
      print('InsightsList: Widget companion ID: ${widget.companionId}');
      setState(() {
        _isLoading = true;
      });

      // Check if there are any conversations with this companion first
      final hasConversations = await _insightsService.hasConversationHistory(widget.companionId);
      print('InsightsList: hasConversationHistory = $hasConversations');

      // Also check if we should show insights based on stored insights
      final shouldShow = await _insightsService.shouldShowInsights(widget.companionId);
      print('InsightsList: shouldShowInsights = $shouldShow');

      if (!hasConversations && !shouldShow) {
        // No conversations yet and no stored insights - show empty state
        print('InsightsList: No conversations and no stored insights - showing empty state');
        setState(() {
          _insights = [];
          _hasConversationHistory = false;
          _isLoading = false;
          _errorMessage = null;
        });
        return;
      }

      // Load real insights from storage
      print('InsightsList: Loading insights from storage...');
      final insights = await _insightsService.getInsights(widget.companionId);
      print('InsightsList: Loaded ${insights.length} insights from storage');

      for (int i = 0; i < insights.length; i++) {
        print('InsightsList: Insight $i: ${insights[i].title} - ${insights[i].category}');
      }

      setState(() {
        _insights = insights;
        _hasConversationHistory = true;
        _isLoading = false;
        _errorMessage = null;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _insights = [];
        _errorMessage = 'Failed to load insights: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: CircularProgressIndicator(
            color: AppTheme.userMessageColor,
          ),
        ),
      );
    }

    // Handle error state
    if (_errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.errorColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppTheme.errorColor.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to Load Insights',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.errorColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'There was an error loading your relationship insights. Please try again later.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadInsights,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Handle empty state (no conversations)
    if (_insights.isEmpty && !_hasConversationHistory) {
      return _buildOnboardingState();
    }

    // Handle empty insights but has conversation history
    if (_insights.isEmpty && _hasConversationHistory) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Icon(
              Icons.lightbulb_outline,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Insights Coming Soon',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Continue chatting to generate personalized relationship insights',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: _insights.map((insight) => _buildInsightCard(context, insight)).toList(),
    );
  }

  Widget _buildInsightCard(BuildContext context, RelationshipInsightModel insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getCategoryColor(insight.category).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with category icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getCategoryColor(insight.category).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(insight.category),
                  color: _getCategoryColor(insight.category),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      insight.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      insight.category.toUpperCase(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getCategoryColor(insight.category),
                        fontWeight: FontWeight.w500,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Description
          Text(
            insight.description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'communication':
        return const Color(0xFF4FC3F7); // Light Blue
      case 'emotional':
        return const Color(0xFFFF8A65); // Orange
      case 'growth':
        return const Color(0xFF81C784); // Green
      case 'connection':
        return const Color(0xFFBA68C8); // Purple
      default:
        return const Color(0xFF4FC3F7); // Sky blue
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'communication':
        return Icons.chat_bubble_outline;
      case 'emotional':
        return Icons.favorite_outline;
      case 'growth':
        return Icons.trending_up;
      case 'connection':
        return Icons.link;
      default:
        return Icons.lightbulb_outline;
    }
  }

  Widget _buildOnboardingState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.userMessageColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.userMessageColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.chat_bubble_outline,
              size: 32,
              color: AppTheme.userMessageColor,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Start Your First Conversation',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Begin chatting with your companion to unlock personalized relationship insights and discover your unique connection.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Tips section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.tips_and_updates,
                      size: 20,
                      color: AppTheme.userMessageColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Conversation Tips',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppTheme.primaryTextColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildTip('Ask about their interests and hobbies'),
                _buildTip('Share something about your day'),
                _buildTip('Discuss your thoughts and feelings'),
                _buildTip('Be genuine and authentic'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.userMessageColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.secondaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
