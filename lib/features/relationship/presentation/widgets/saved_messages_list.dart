import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/services/api_service.dart';
import '../../../chat/data/chat_service.dart';

class SavedMessagesList extends StatefulWidget {
  final String companionId;

  const SavedMessagesList({
    super.key,
    required this.companionId,
  });

  @override
  State<SavedMessagesList> createState() => _SavedMessagesListState();
}

class _SavedMessagesListState extends State<SavedMessagesList> {
  late ChatService _chatService;
  List<MessageModel> _savedMessages = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _chatService = ChatService(
      firebaseService: FirebaseService(),
      apiService: ApiService(),
    );
    _loadSavedMessages();
  }

  @override
  void didUpdateWidget(SavedMessagesList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reload saved messages if companion changed
    if (oldWidget.companionId != widget.companionId) {
      _loadSavedMessages();
    }
  }

  Future<void> _loadSavedMessages() async {
    try {
      Logger.debug('Loading saved messages for companion: ${widget.companionId}', tag: 'SavedMessagesList');
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final messages = await _chatService.getSavedMessages(widget.companionId);
      Logger.debug('Loaded ${messages.length} saved messages', tag: 'SavedMessagesList');

      setState(() {
        _savedMessages = messages;
        _isLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading saved messages', tag: 'SavedMessagesList', error: e);
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load saved messages: ${e.toString()}';
      });
    }
  }

  Future<void> _removeSavedMessage(MessageModel message) async {
    try {
      // Remove from Firebase
      await _chatService.saveMessage(
        message.conversationId,
        message.id,
        false, // Set isSaved to false
      );

      // Remove from local list
      setState(() {
        _savedMessages.removeWhere((m) => m.id == message.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Message removed from saved'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      }
    } catch (e) {
      Logger.error('Error removing saved message', tag: 'SavedMessagesList', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove message: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  // Public method to refresh saved messages (can be called from parent)
  void refreshSavedMessages() {
    _loadSavedMessages();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppTheme.userMessageColor,
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppTheme.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSavedMessages,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.userMessageColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_savedMessages.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Icon(
              Icons.bookmark_outline,
              size: 48,
              color: AppTheme.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Saved Messages',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.secondaryTextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Long press on messages in chat to save your favorite moments',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.hintTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: _savedMessages.map((message) => _buildSavedMessageCard(
        context,
        message,
      )).toList(),
    );
  }

  Widget _buildSavedMessageCard(BuildContext context, MessageModel message) {
    final timeFormat = DateFormat('MMM dd, yyyy • HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.userMessageColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                message.isFromUser ? Icons.person : Icons.psychology,
                color: message.isFromUser 
                    ? AppTheme.userMessageColor
                    : AppTheme.friendlyColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                message.isFromUser ? 'You' : 'Companion',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: message.isFromUser 
                      ? AppTheme.userMessageColor
                      : AppTheme.friendlyColor,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.bookmark,
                color: AppTheme.userMessageColor,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                timeFormat.format(message.timestamp),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.hintTextColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Message Content
          Text(
            message.content,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.primaryTextColor,
              height: 1.4,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Actions
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  // Copy to clipboard
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Message copied to clipboard'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.copy,
                        size: 16,
                        color: AppTheme.secondaryTextColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Copy',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () => _removeSavedMessage(message),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.bookmark_remove,
                        size: 16,
                        color: AppTheme.secondaryTextColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Remove',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }


}
