import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../shared/widgets/companion_avatar.dart';
import '../../../chat/presentation/bloc/chat_bloc.dart';
import '../../../chat/presentation/bloc/chat_state.dart';
import '../../../chat/presentation/bloc/chat_event.dart';
import '../../../chat/data/chat_service.dart';
import '../widgets/relationship_stats_card.dart';
import '../widgets/insights_list.dart';

class RelationshipPage extends StatefulWidget {
  const RelationshipPage({super.key});

  @override
  State<RelationshipPage> createState() => _RelationshipPageState();
}

class _RelationshipPageState extends State<RelationshipPage> {
  int _refreshCounter = 0; // Used to force insights widget rebuild

  Future<void> _refreshData() async {
    try {
      final chatBloc = context.read<ChatBloc>();
      final currentState = chatBloc.state;

      if (currentState.selectedCompanion == null) {
        return;
      }

      print('RelationshipPage: Refreshing data for companion ${currentState.selectedCompanion!.name}');

      // 1. Refresh companion data from Firebase to get latest scores and relationship info
      final chatService = ChatService(
        firebaseService: FirebaseService(),
        apiService: ApiService(),
      );

      final companions = await chatService.firebaseService.getUserCompanions(
        currentState.selectedCompanion!.userId,
      );

      final updatedCompanion = companions.firstWhere(
        (c) => c.id == currentState.selectedCompanion!.id,
        orElse: () => currentState.selectedCompanion!,
      );

      print('RelationshipPage: Updated companion score: ${updatedCompanion.relationshipScore}');

      // 2. Update the storage with fresh companion data BEFORE triggering the event
      final storageService = StorageService();
      await storageService.saveCompanions(companions);
      await storageService.saveSelectedCompanion(updatedCompanion);

      // 3. Now trigger the companion change event which will use the fresh data
      chatBloc.add(ChatCompanionChanged(updatedCompanion.id));

      // 3. Force insights widget to rebuild by changing its key
      setState(() {
        _refreshCounter++;
      });

      print('RelationshipPage: Data refresh completed');

    } catch (e) {
      print('Error refreshing relationship data: $e');
      // Optionally show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to refresh data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, chatState) {
          final selectedCompanion = chatState.selectedCompanion;
          
          if (selectedCompanion == null) {
            return _buildNoCompanionSelected(context);
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            color: Theme.of(context).colorScheme.primary,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: CustomScrollView(
              slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    selectedCompanion.name,
                    style: TextStyle(
                      color: Theme.of(context).appBarTheme.foregroundColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primary.withOpacity(0.8),
                          Theme.of(context).colorScheme.primary.withOpacity(0.6),
                        ],
                      ),
                    ),
                    child: Center(
                      child: CompanionAvatarLarge(
                        imageUrl: selectedCompanion.imageUrl,
                      ),
                    ),
                  ),
                ),
              ),
              
              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Relationship Stats
                      RelationshipStatsCard(companion: selectedCompanion),

                      const SizedBox(height: 32),

                      // Relationship Description
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: selectedCompanion.relationshipStatusColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.psychology,
                                  color: selectedCompanion.relationshipStatusColor,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Relationship Insights',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              selectedCompanion.relationshipDescription,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                height: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Relationship Insights Section
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Relationship Insights',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                      
                        ],
                      ),
                      const SizedBox(height: 16),
                      InsightsList(
                        key: ValueKey('insights_${selectedCompanion.id}_$_refreshCounter'),
                        companionId: selectedCompanion.id,
                      ),
                    ],
                  ),
                ),
              ),
            ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoCompanionSelected(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_outline,
            size: 80,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Companion Selected',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a companion from the chat tab to view your relationship details',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              // Navigate to chat tab
              DefaultTabController.of(context).animateTo(0);
            },
            child: const Text('Go to Chat'),
          ),
        ],
      ),
    );
  }
}
