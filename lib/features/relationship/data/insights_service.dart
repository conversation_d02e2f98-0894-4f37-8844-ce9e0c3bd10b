import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/firebase_service.dart';
import '../../../shared/models/relationship_insight_model.dart';

class InsightsService {
  final StorageService _storageService;
  final FirebaseService _firebaseService;

  InsightsService({
    required StorageService storageService,
    required FirebaseService firebaseService,
  }) : _storageService = storageService,
       _firebaseService = firebaseService;

  /// Store insights for a specific companion
  Future<void> storeInsights(String companionId, List<RelationshipInsightModel> insights) async {
    try {
      print('InsightsService: Storing ${insights.length} insights for companion $companionId');
      final insightsData = insights.map((insight) => insight.toJson()).toList();
      await _storageService.saveData('insights_$companionId', insightsData);
      print('InsightsService: Successfully stored insights');
    } catch (e) {
      print('InsightsService: Error storing insights: $e');
      throw Exception('Failed to store insights: ${e.toString()}');
    }
  }

  /// Get stored insights for a specific companion
  Future<List<RelationshipInsightModel>> getInsights(String companionId) async {
    try {
      print('InsightsService: Getting insights for companion $companionId');
      final insightsData = _storageService.getData('insights_$companionId');
      print('InsightsService: Raw insights data: $insightsData');

      if (insightsData == null) {
        print('InsightsService: No insights data found');
        return [];
      }

      if (insightsData is! List) {
        print('InsightsService: Insights data is not a list: ${insightsData.runtimeType}');
        return [];
      }

      final insightsList = insightsData;
      print('InsightsService: Processing ${insightsList.length} insights');

      final results = insightsList
          .map((insight) {
            try {
              return RelationshipInsightModel.fromJson(insight as Map<String, dynamic>);
            } catch (e) {
              print('InsightsService: Error parsing insight: $e');
              // Skip invalid insight data
              return null;
            }
          })
          .where((insight) => insight != null)
          .cast<RelationshipInsightModel>()
          .toList();

      print('InsightsService: Returning ${results.length} valid insights');
      return results;
    } catch (e) {
      print('InsightsService: Error getting insights: $e');
      // Return empty list instead of throwing to prevent UI crashes
      return [];
    }
  }

  /// Clear insights for a specific companion
  Future<void> clearInsights(String companionId) async {
    try {
      await _storageService.removeData('insights_$companionId');
    } catch (e) {
      throw Exception('Failed to clear insights: ${e.toString()}');
    }
  }

  /// Check if there's any conversation history with a companion
  Future<bool> hasConversationHistory(String companionId) async {
    try {
      // First check if there are any saved messages
      final savedMessages = await _firebaseService.getSavedMessages(companionId);
      if (savedMessages.isNotEmpty) {
        return true;
      }

      // Check if there's a conversation with this companion by trying to get any messages
      // We'll use a direct Firestore query to check for any messages in conversations with this companion
      try {
        final conversationQuery = await FirebaseFirestore.instance
            .collection('conversations')
            .where('companion_id', isEqualTo: companionId)
            .limit(1)
            .get();

        if (conversationQuery.docs.isEmpty) {
          return false;
        }

        final conversationId = conversationQuery.docs.first.id;

        // Check if this conversation has any messages
        final messagesQuery = await FirebaseFirestore.instance
            .collection('conversations')
            .doc(conversationId)
            .collection('messages')
            .limit(1)
            .get();

        return messagesQuery.docs.isNotEmpty;
      } catch (e) {
        // If direct query fails, fall back to assuming no history
        return false;
      }
    } catch (e) {
      // If we can't check, assume no history to be safe
      return false;
    }
  }

  /// Check if insights should be shown (simplified version)
  /// For now, always return true if there are stored insights, regardless of conversation history
  Future<bool> shouldShowInsights(String companionId) async {
    try {
      final insights = await getInsights(companionId);
      return insights.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get mock insights for development/testing
  List<RelationshipInsightModel> getMockInsights() {
    return [
      const RelationshipInsightModel(
        title: 'Growing Connection',
        description: 'Your conversations show increasing emotional depth and trust. You\'re both opening up more and sharing personal thoughts.',
        category: 'connection',
      ),
      const RelationshipInsightModel(
        title: 'Communication Style',
        description: 'You have a natural, flowing conversation style that makes your companion feel comfortable and heard.',
        category: 'communication',
      ),
      const RelationshipInsightModel(
        title: 'Emotional Support',
        description: 'You provide excellent emotional support and show genuine care for your companion\'s feelings and experiences.',
        category: 'emotional',
      ),
      const RelationshipInsightModel(
        title: 'Future Growth',
        description: 'There\'s potential for even deeper connection through sharing more personal stories and experiences together.',
        category: 'growth',
      ),
    ];
  }
}
