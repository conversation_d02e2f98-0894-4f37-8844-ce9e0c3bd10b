import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/presentation/bloc/auth_state.dart';
import '../bloc/profile_bloc.dart';
import '../bloc/profile_event.dart';
import '../bloc/profile_state.dart';
import '../widgets/gender_selection_widget.dart';
import '../widgets/profile_image_picker.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({super.key});

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  late TextEditingController _usernameController;
  String _selectedGender = '';
  String? _imageUrl;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController();
    
    // Initialize with current user data
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      _usernameController.text = authState.user.username;
      _selectedGender = authState.user.gender;
      _imageUrl = authState.user.profileImageUrl;
    }
    
    _usernameController.addListener(_onDataChanged);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  void _onDataChanged() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final hasUsernameChanged = _usernameController.text != authState.user.username;
      final hasGenderChanged = _selectedGender != authState.user.gender;
      final hasImageChanged = _imageUrl != authState.user.profileImageUrl;
      
      final newHasChanges = hasUsernameChanged || hasGenderChanged || hasImageChanged;
      if (newHasChanges != _hasChanges) {
        setState(() {
          _hasChanges = newHasChanges;
        });
      }
    }
  }

  void _onGenderSelected(String gender) {
    setState(() {
      _selectedGender = gender;
    });
    _onDataChanged();
  }

  void _onImageSelected(String? imageUrl) {
    setState(() {
      _imageUrl = imageUrl;
    });
    _onDataChanged();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Edit Profile',
          style: TextStyle(
            color: Theme.of(context).appBarTheme.foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
      ),
      body: BlocListener<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }

          if (state.isSubmitted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile updated successfully!'),
                backgroundColor: AppTheme.successColor,
              ),
            );
            Navigator.pop(context);
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Image Section
              Center(
                child: ProfileImagePicker(
                  imageUrl: _imageUrl,
                  onImageSelected: _onImageSelected,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Username Section
              Text(
                'Username',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _usernameController,
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                decoration: InputDecoration(
                  hintText: 'Enter your username',
                  hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Gender Section
              Text(
                'Gender',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              GenderSelectionWidget(
                selectedGender: _selectedGender,
                onGenderSelected: _onGenderSelected,
              ),
              
              const SizedBox(height: 40),
              
              // Save Button
              BlocBuilder<ProfileBloc, ProfileState>(
                builder: (context, state) {
                  final isValid = _usernameController.text.isNotEmpty && 
                                 _selectedGender.isNotEmpty;

                  return SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: isValid && _hasChanges && !state.isLoading
                          ? () {
                              context.read<ProfileBloc>().add(ProfileSubmitted(
                                username: _usernameController.text,
                                gender: _selectedGender,
                                imageUrl: _imageUrl,
                              ));
                            }
                          : null,
                      child: state.isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppTheme.primaryTextColor,
                                ),
                              ),
                            )
                          : Text(_hasChanges ? 'Save Changes' : 'No Changes'),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
