import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../bloc/profile_bloc.dart';
import '../bloc/profile_event.dart';
import '../bloc/profile_state.dart';
import '../widgets/gender_selection_widget.dart';
import '../widgets/profile_image_picker.dart';

class ProfileSetupPage extends StatefulWidget {
  const ProfileSetupPage({super.key});

  @override
  State<ProfileSetupPage> createState() => _ProfileSetupPageState();
}

class _ProfileSetupPageState extends State<ProfileSetupPage> {
  String _username = '';
  String _selectedGender = '';
  String? _imageUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).scaffoldBackgroundColor,
                Theme.of(context).scaffoldBackgroundColor.withOpacity(0.8),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(
                          Icons.arrow_back,
                          color: Theme.of(context).colorScheme.onBackground,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Profile Setup',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onBackground,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                  
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Welcome Text
                          Text(
                            'Let\'s get to know you',
                            style: Theme.of(context).textTheme.displaySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Tell us a bit about yourself to personalize your experience',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                          
                          const SizedBox(height: 40),
                          
                          // Profile Image
                          Center(
                            child: ProfileImagePicker(
                              imageUrl: _imageUrl,
                              onImageSelected: (imageUrl) {
                                setState(() {
                                  _imageUrl = imageUrl;
                                });
                              },
                            ),
                          ),
                          
                          const SizedBox(height: 40),
                          
                          // Username Field
                          Text(
                            'Username',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            initialValue: _username,
                            onChanged: (value) {
                              setState(() {
                                _username = value;
                              });
                            },
                            decoration: const InputDecoration(
                              hintText: 'Enter your username',
                              prefixIcon: Icon(Icons.person_outline),
                            ),
                            style: const TextStyle(color: AppTheme.primaryTextColor),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Gender Selection
                          Text(
                            'Gender',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 16),
                          GenderSelectionWidget(
                            selectedGender: _selectedGender,
                            onGenderSelected: (gender) {
                              setState(() {
                                _selectedGender = gender;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Continue Button
                  BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, state) {
                      final isValid = _username.isNotEmpty && _selectedGender.isNotEmpty;

                      return SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: isValid && !state.isLoading
                              ? () {
                                  print('🔵 ProfileSetupPage: Continue button pressed');
                                  print('🔵 ProfileSetupPage: username="$_username", gender="$_selectedGender", imageUrl="$_imageUrl"');
                                  // Submit all local state directly to BLoC
                                  context.read<ProfileBloc>().add(ProfileSubmitted(
                                    username: _username,
                                    gender: _selectedGender,
                                    imageUrl: _imageUrl,
                                  ));
                                  print('🔵 ProfileSetupPage: ProfileSubmitted event dispatched');
                                }
                              : null,
                          child: state.isLoading
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppTheme.primaryTextColor,
                                    ),
                                  ),
                                )
                              : const Text('Continue'),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
