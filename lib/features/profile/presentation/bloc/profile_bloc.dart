import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/firebase_service.dart';
import '../../data/profile_service.dart';
import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final StorageService _storageService;
  final ProfileService _profileService;

  ProfileBloc({
    required StorageService storageService,
    required FirebaseService firebaseService,
  })  : _storageService = storageService,
        _profileService = ProfileService(
          firebaseService: firebaseService,
          storageService: storageService,
        ),
        super(const ProfileState()) {
    on<ProfileUsernameChanged>(_onUsernameChanged);
    on<ProfileGenderChanged>(_onGenderChanged);
    on<ProfileImageChanged>(_onImageChanged);
    on<ProfileSubmitted>(_onSubmitted);
    on<ProfileReset>(_onReset);
  }

  void _onUsernameChanged(
    ProfileUsernameChanged event,
    Emitter<ProfileState> emit,
  ) {
    final newState = state.copyWith(
      username: event.username,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidProfile(newState)));
  }

  void _onGenderChanged(
    ProfileGenderChanged event,
    Emitter<ProfileState> emit,
  ) {
    final newState = state.copyWith(
      gender: event.gender,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidProfile(newState)));
  }

  void _onImageChanged(
    ProfileImageChanged event,
    Emitter<ProfileState> emit,
  ) {
    final newState = state.copyWith(
      imageUrl: event.imageUrl,
      errorMessage: null,
    );
    emit(newState.copyWith(isValid: _isValidProfile(newState)));
  }

  Future<void> _onSubmitted(
    ProfileSubmitted event,
    Emitter<ProfileState> emit,
  ) async {
    // Validate the submitted data
    if (event.username.trim().isEmpty ||
        event.username.trim().length < 2 ||
        event.gender.isEmpty) {
      emit(state.copyWith(
        errorMessage: 'Please fill in all required fields',
      ));
      return;
    }

    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Get current user
      final currentUser = _storageService.getUser();
      if (currentUser == null) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'User not found',
        ));
        return;
      }

      // Update user profile using ProfileService with event data
      await _profileService.updateUserProfile(
        userId: currentUser.id,
        username: event.username,
        gender: event.gender,
        profileImageUrl: event.imageUrl,
      );

      emit(state.copyWith(
        isLoading: false,
        isSubmitted: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  void _onReset(
    ProfileReset event,
    Emitter<ProfileState> emit,
  ) {
    emit(const ProfileState());
  }

  bool _isValidProfile(ProfileState state) {
    return state.username.trim().isNotEmpty &&
           state.username.trim().length >= 2 &&
           state.gender.isNotEmpty;
  }
}
