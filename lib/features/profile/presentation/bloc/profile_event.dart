import 'package:equatable/equatable.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class ProfileUsernameChanged extends ProfileEvent {
  final String username;

  const ProfileUsernameChanged(this.username);

  @override
  List<Object?> get props => [username];
}

class ProfileGenderChanged extends ProfileEvent {
  final String gender;

  const ProfileGenderChanged(this.gender);

  @override
  List<Object?> get props => [gender];
}

class ProfileImageChanged extends ProfileEvent {
  final String? imageUrl;

  const ProfileImageChanged(this.imageUrl);

  @override
  List<Object?> get props => [imageUrl];
}

class ProfileSubmitted extends ProfileEvent {
  final String username;
  final String gender;
  final String? imageUrl;

  const ProfileSubmitted({
    required this.username,
    required this.gender,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [username, gender, imageUrl];
}

class ProfileReset extends ProfileEvent {}
