import 'package:equatable/equatable.dart';

class ProfileState extends Equatable {
  final String username;
  final String gender;
  final String? imageUrl;
  final bool isValid;
  final bool isLoading;
  final String? errorMessage;
  final bool isSubmitted;

  const ProfileState({
    this.username = '',
    this.gender = '',
    this.imageUrl,
    this.isValid = false,
    this.isLoading = false,
    this.errorMessage,
    this.isSubmitted = false,
  });

  ProfileState copyWith({
    String? username,
    String? gender,
    String? imageUrl,
    bool? isValid,
    bool? isLoading,
    String? errorMessage,
    bool? isSubmitted,
  }) {
    return ProfileState(
      username: username ?? this.username,
      gender: gender ?? this.gender,
      imageUrl: imageUrl ?? this.imageUrl,
      isValid: isValid ?? this.isValid,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      isSubmitted: isSubmitted ?? this.isSubmitted,
    );
  }

  @override
  List<Object?> get props => [
        username,
        gender,
        imageUrl,
        isValid,
        isLoading,
        errorMessage,
        isSubmitted,
      ];
}
