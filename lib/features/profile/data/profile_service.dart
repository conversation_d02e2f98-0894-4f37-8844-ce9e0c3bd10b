import '../../../core/services/firebase_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../shared/models/user_model.dart';

class ProfileService {
  final FirebaseService _firebaseService;
  final StorageService _storageService;

  ProfileService({
    required FirebaseService firebaseService,
    required StorageService storageService,
  })  : _firebaseService = firebaseService,
        _storageService = storageService;

  Future<void> updateUserProfile({
    required String userId,
    required String username,
    required String gender,
    String? profileImageUrl,
  }) async {
    print('🔵 ProfileService: updateUserProfile started');
    print('🔵 ProfileService: userId=$userId, username=$username, gender=$gender, profileImageUrl=$profileImageUrl');

    try {
      // Get current user
      print('🔵 ProfileService: Getting user from Firebase...');
      final currentUser = await _firebaseService.getUserById(userId);
      if (currentUser == null) {
        print('🔴 ProfileService: User not found in Firebase');
        throw Exception('User not found');
      }
      print('🔵 ProfileService: Current user found - email: ${currentUser.email}, currentUsername: ${currentUser.username}, currentGender: ${currentUser.gender}');

      // Update user with new profile data
      print('🔵 ProfileService: Creating updated user...');
      final updatedUser = currentUser.copyWith(
        username: username,
        gender: gender,
        profileImageUrl: profileImageUrl,
        isOnboardingComplete: true,
        updatedAt: DateTime.now(),
      );
      print('🔵 ProfileService: Updated user created - username: ${updatedUser.username}, gender: ${updatedUser.gender}, onboardingComplete: ${updatedUser.isOnboardingComplete}');

      // Save to Firestore and local storage
      print('🔵 ProfileService: Updating user in Firebase...');
      await _firebaseService.updateUser(updatedUser);
      print('🔵 ProfileService: Firebase update completed');

      print('🔵 ProfileService: Saving user to local storage...');
      await _storageService.saveUser(updatedUser);
      print('🔵 ProfileService: Local storage save completed');

      print('🟢 ProfileService: updateUserProfile completed successfully');
    } catch (e) {
      print('🔴 ProfileService: Error in updateUserProfile: $e');
      print('🔴 ProfileService: Error type: ${e.runtimeType}');
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser != null) {
        return await _firebaseService.getUserById(firebaseUser.uid);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get current user: ${e.toString()}');
    }
  }


}
