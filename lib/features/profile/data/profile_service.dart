import '../../../core/services/firebase_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../shared/models/user_model.dart';

class ProfileService {
  final FirebaseService _firebaseService;
  final StorageService _storageService;

  ProfileService({
    required FirebaseService firebaseService,
    required StorageService storageService,
  })  : _firebaseService = firebaseService,
        _storageService = storageService;

  Future<void> updateUserProfile({
    required String userId,
    required String username,
    required String gender,
    String? profileImageUrl,
  }) async {
    try {
      // Get current user
      final currentUser = await _firebaseService.getUserById(userId);
      if (currentUser == null) {
        throw Exception('User not found');
      }

      // Update user with new profile data
      final updatedUser = currentUser.copyWith(
        username: username,
        gender: gender,
        profileImageUrl: profileImageUrl,
        isOnboardingComplete: true,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore and local storage
      await _firebaseService.updateUser(updatedUser);
      await _storageService.saveUser(updatedUser);
    } catch (e) {
      throw Exception('Failed to update profile: ${e.toString()}');
    }
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser != null) {
        return await _firebaseService.getUserById(firebaseUser.uid);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get current user: ${e.toString()}');
    }
  }


}
