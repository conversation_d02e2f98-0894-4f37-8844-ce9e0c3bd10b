import 'package:flutter/material.dart';

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Terms of Service',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Theme.of(context).colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms of Service',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().year}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              context,
              'Acceptance of Terms',
              'By accessing and using Nova Soul, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
            ),
            
            _buildSection(
              context,
              'Description of Service',
              'Nova Soul is an AI companion application that provides personalized conversational experiences through artificial intelligence. The service includes chat functionality, relationship building features, and companion customization options.',
            ),
            
            _buildSection(
              context,
              'User Responsibilities',
              'You are responsible for:\n• Maintaining the confidentiality of your account\n• All activities that occur under your account\n• Ensuring your use complies with applicable laws\n• Treating AI companions and other users with respect',
            ),
            
            _buildSection(
              context,
              'Prohibited Uses',
              'You may not use Nova Soul to:\n• Engage in illegal activities\n• Harass, abuse, or harm others\n• Attempt to hack or compromise the service\n• Share inappropriate or harmful content\n• Violate any applicable laws or regulations',
            ),
            
            _buildSection(
              context,
              'AI Companion Interactions',
              'Our AI companions are artificial intelligence systems designed to provide engaging conversations. While they may seem lifelike, they are not real people. All interactions are with AI systems, and responses are generated by machine learning algorithms.',
            ),
            
            _buildSection(
              context,
              'Content and Intellectual Property',
              'All content, features, and functionality of Nova Soul are owned by us and are protected by copyright, trademark, and other intellectual property laws. You may not reproduce, distribute, or create derivative works without permission.',
            ),
            
            _buildSection(
              context,
              'Privacy and Data',
              'Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information. By using our service, you consent to our data practices as described in the Privacy Policy.',
            ),
            
            _buildSection(
              context,
              'Service Availability',
              'We strive to maintain service availability but cannot guarantee uninterrupted access. We may modify, suspend, or discontinue the service at any time with or without notice.',
            ),
            
            _buildSection(
              context,
              'Limitation of Liability',
              'Nova Soul is provided "as is" without warranties of any kind. We shall not be liable for any indirect, incidental, special, or consequential damages resulting from your use of the service.',
            ),
            
            _buildSection(
              context,
              'Termination',
              'We may terminate or suspend your account and access to the service immediately, without prior notice, for conduct that we believe violates these Terms of Service or is harmful to other users or the service.',
            ),
            
            _buildSection(
              context,
              'Changes to Terms',
              'We reserve the right to modify these terms at any time. We will notify users of any changes by posting the new Terms of Service in the app and updating the "Last updated" date.',
            ),
            
            _buildSection(
              context,
              'Contact Information',
              'If you have any questions about these Terms of Service, please contact us through the app\'s support features.',
            ),
            
            const SizedBox(height: 32),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.description,
                    size: 32,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Agreement',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'By using Nova Soul, you agree to these terms and conditions.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
