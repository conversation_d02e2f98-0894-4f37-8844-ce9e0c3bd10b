import 'dart:io';
import 'package:uuid/uuid.dart';
import '../../../core/services/firebase_service.dart';
import '../../../core/services/api_service.dart';
import '../../../shared/models/message_model.dart';
import '../../../shared/models/companion_model.dart';

class ChatService {
  final FirebaseService _firebaseService;
  final ApiService _apiService;

  ChatService({
    required FirebaseService firebaseService,
    required ApiService apiService,
  })  : _firebaseService = firebaseService,
        _apiService = apiService;

  // Getter to access firebase service for relationship updates
  FirebaseService get firebaseService => _firebaseService;

  Stream<List<MessageModel>> getConversationMessages(String conversationId) {
    return _firebaseService.getConversationMessages(conversationId);
  }

  Future<void> sendMessage({
    required String conversationId,
    required String content,
    required String sender,
    String? scenarioId,
    String? gameId,
    String? imageUrl,
  }) async {
    try {
      final message = MessageModel(
        id: const Uuid().v4(),
        conversationId: conversationId,
        content: content,
        sender: sender,
        timestamp: DateTime.now(),
        scenarioId: scenarioId,
        gameId: gameId,
        imageUrl: imageUrl,
      );

      await _firebaseService.sendMessage(message);
    } catch (e) {
      throw Exception('Failed to send message: ${e.toString()}');
    }
  }

  Future<Map<String, dynamic>?> sendMessageToAI({
    String? conversationId,
    required String userMessage,
    required CompanionModel companion,
    bool isGame = false,
    bool isScenario = false,
    String? selectedGame,
    String? selectedScenario,
    File? imageFile,
  }) async {
    try {
      // Call AI API with the message (empty for image-only)
      final response = await _apiService.sendChatMessage(
        message: userMessage, // Can be empty for image-only
        userId: companion.userId,
        conversationId: conversationId,
        isGame: isGame,
        isScenario: isScenario,
        selectedGame: selectedGame,
        selectedScenario: selectedScenario,
        imageFile: imageFile,
   
      );

      // The backend handles saving both user message and AI response
      // Return the response so we can get the conversation_id
      return response;
    } catch (e) {
      throw Exception('Failed to get AI response: ${e.toString()}');
    }
  }

  // Send image message to AI (convenience method)
  Future<Map<String, dynamic>?> sendImageToAI({
    String? conversationId,
    required File imageFile,
    required CompanionModel companion,
    bool isGame = false,
    bool isScenario = false,
    String? selectedGame,
    String? selectedScenario,
  }) async {
    return await sendMessageToAI(
      conversationId: conversationId,
      userMessage: '', // Empty message for image-only
      companion: companion,
      isGame: isGame,
      isScenario: isScenario,
      selectedGame: selectedGame,
      selectedScenario: selectedScenario,
      imageFile: imageFile,
    );
  }



  Future<void> saveMessage(String conversationId, String messageId, bool isSaved) async {
    try {
      await _firebaseService.updateMessageSaveStatus(conversationId, messageId, isSaved);
    } catch (e) {
      throw Exception('Failed to save message: ${e.toString()}');
    }
  }

  Future<List<MessageModel>> getSavedMessages(String companionId) async {
    try {
      return await _firebaseService.getSavedMessages(companionId);
    } catch (e) {
      throw Exception('Failed to get saved messages: ${e.toString()}');
    }
  }

  Future<List<MessageModel>> getAllSavedMessages(String userId) async {
    try {
      return await _firebaseService.getAllSavedMessages(userId);
    } catch (e) {
      throw Exception('Failed to get all saved messages: ${e.toString()}');
    }
  }

  Future<List<MessageModel>> getSavedMediaMessages(String companionId) async {
    try {
      final allSavedMessages = await _firebaseService.getSavedMessages(companionId);
      // Filter messages that have images
      return allSavedMessages.where((message) => message.hasImage).toList();
    } catch (e) {
      throw Exception('Failed to get saved media messages: ${e.toString()}');
    }
  }
}
