import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/firebase_service.dart';
import '../../../../core/services/usage_tracking_service.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../shared/models/relationship_insight_model.dart';
import '../../../../core/utils/logger.dart';
import '../../../relationship/data/insights_service.dart';
import '../../data/chat_service.dart';
import 'chat_event.dart';
import 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final StorageService _storageService;
  final ChatService _chatService;
  final InsightsService _insightsService;
  final UsageTrackingService _usageTrackingService;

  ChatBloc({
    required ApiService apiService,
    required StorageService storageService,
    required FirebaseService firebaseService,
    required UsageTrackingService usageTrackingService,
  })  : _storageService = storageService,
        _chatService = ChatService(
          firebaseService: firebaseService,
          apiService: apiService,
        ),
        _insightsService = InsightsService(
          storageService: storageService,
          firebaseService: firebaseService,
        ),
        _usageTrackingService = usageTrackingService,
        super(_getInitialState(storageService)) {
    on<ChatMessagesLoaded>(_onMessagesLoaded);
    on<ChatMessageSent>(_onMessageSent);
    on<ChatMessageReceived>(_onMessageReceived);
    on<ChatMessageSaved>(_onMessageSaved);
    on<ChatCompanionChanged>(_onCompanionChanged);
    on<ChatScenarioActivated>(_onScenarioActivated);
    on<ChatGameActivated>(_onGameActivated);
    on<ChatScenarioGameCancelled>(_onScenarioGameCancelled);
    on<ChatRelationshipScoreUpdated>(_onRelationshipScoreUpdated);
    on<ChatReset>(_onReset);
  }

  static ChatState _getInitialState(StorageService storageService) {
    try {
      final activeScenario = storageService.getActiveScenario();
      final activeGame = storageService.getActiveGame();

      return ChatState(
        activeScenarioId: activeScenario,
        activeGameId: activeGame,
      );
    } catch (e) {
      return const ChatState();
    }
  }

  Future<void> _onMessagesLoaded(
    ChatMessagesLoaded event,
    Emitter<ChatState> emit,
  ) async {
    Logger.debug('Loading messages for conversation: ${event.conversationId}', tag: 'ChatBloc');
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Use emit.forEach to properly handle stream emissions
      await emit.forEach<List<MessageModel>>(
        _chatService.getConversationMessages(event.conversationId),
        onData: (messages) {
          Logger.debug('Received ${messages.length} messages', tag: 'ChatBloc');
          return state.copyWith(
            isLoading: false,
            messages: messages,
            currentConversationId: event.conversationId,
            errorMessage: null,
          );
        },
        onError: (error, stackTrace) {
          Logger.error('Error loading messages', tag: 'ChatBloc', error: error);
          return state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to load messages: ${error.toString()}',
          );
        },
      );
    } catch (e) {
      Logger.error('Exception loading messages', tag: 'ChatBloc', error: e);
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load messages: ${e.toString()}',
      ));
    }
  }

  Future<void> _onMessageSent(
    ChatMessageSent event,
    Emitter<ChatState> emit,
  ) async {
    if (state.selectedCompanion == null) {
      emit(state.copyWith(errorMessage: 'No companion selected'));
      return;
    }

    // Check usage limits
    final canSendMessage = await _usageTrackingService.canSendMessage();
    if (!canSendMessage) {
      final usageStatus = await _usageTrackingService.getUsageStatus();
      emit(state.copyWith(
        errorMessage: usageStatus.limitMessage ?? 'Daily message limit reached',
        isSending: false,
      ));
      return;
    }

    // Check image sending limits if image is attached
    if (event.imageFile != null) {
      final canSendImage = await _usageTrackingService.canSendImage();
      if (!canSendImage) {
        emit(state.copyWith(
          errorMessage: 'Daily image limit reached. Upgrade for more images!',
          isSending: false,
        ));
        return;
      }
    }

    emit(state.copyWith(isSending: true, errorMessage: null));

    try {
      // Determine if this is a scenario/game activation
      bool isActivatingScenario = event.scenarioId != null;
      bool isActivatingGame = event.gameId != null;

      // Use active scenario/game if not activating new ones
      String? effectiveScenarioId = isActivatingScenario ? event.scenarioId : state.activeScenarioId;
      String? effectiveGameId = isActivatingGame ? event.gameId : state.activeGameId;

      // Debug logging
      Logger.debug('Sending message: "${event.message}"', tag: 'ChatBloc');
      Logger.debug('isActivatingScenario: $isActivatingScenario, isActivatingGame: $isActivatingGame', tag: 'ChatBloc');
      Logger.debug('effectiveScenarioId: $effectiveScenarioId, effectiveGameId: $effectiveGameId', tag: 'ChatBloc');

      // Send message to AI using ChatService
      // Keep the original message (empty for activation) - backend will handle "Let's start"
      final response = await _chatService.sendMessageToAI(
        conversationId: state.currentConversationId,
        userMessage: event.message, // Send original message (empty for activation)
        companion: state.selectedCompanion!,
        isGame: effectiveGameId != null,
        isScenario: effectiveScenarioId != null,
        selectedGame: effectiveGameId,
        selectedScenario: effectiveScenarioId,
        imageFile: event.imageFile,
      );

      // Update conversation ID if we got a new one from the backend
      if (response != null && response['conversation_id'] != null) {
        final newConversationId = response['conversation_id'] as String;
        if (newConversationId != state.currentConversationId) {
          // Update the companion with the new conversation ID
          final updatedCompanion = state.selectedCompanion!.copyWith(
            conversationId: newConversationId,
          );
          await _storageService.saveSelectedCompanion(updatedCompanion);

          emit(state.copyWith(
            currentConversationId: newConversationId,
            selectedCompanion: updatedCompanion,
            isSending: false,
          ));

          // Load messages for the new conversation
          add(ChatMessagesLoaded(newConversationId));
          return;
        }
      }

      // Handle relationship score updates from backend
      if (response != null && response['relationship_update'] != null) {
        final relationshipUpdate = response['relationship_update'] as Map<String, dynamic>;
        final previousScore = relationshipUpdate['previous_score'] as int;
        final newScore = relationshipUpdate['new_score'] as int;
        final scoreChange = relationshipUpdate['score_change'] as int;
        final description = relationshipUpdate['description'] as String?;

        // Parse insights if available
        List<RelationshipInsightModel>? insights;
        if (relationshipUpdate['insights'] != null) {
          final insightsData = relationshipUpdate['insights'] as List<dynamic>;
          insights = insightsData
              .map((insight) => RelationshipInsightModel.fromJson(insight as Map<String, dynamic>))
              .toList();

          // Log insights for debugging
          Logger.info('Received ${insights.length} insights from backend', tag: 'ChatBloc');
          for (final insight in insights) {
            Logger.info('Insight: ${insight.title} - ${insight.category}', tag: 'ChatBloc');
          }
        } else {
          Logger.warning('No insights received from backend', tag: 'ChatBloc');
        }

        // Trigger relationship score update event
        add(ChatRelationshipScoreUpdated(
          previousScore: previousScore,
          newScore: newScore,
          scoreChange: scoreChange,
          description: description,
          insights: insights,
        ));
      } else {
        Logger.warning('No relationship_update in response', tag: 'ChatBloc');
      }

      // Increment usage counters after successful message sending
      await _usageTrackingService.incrementMessageCount();
      if (event.imageFile != null) {
        await _usageTrackingService.incrementImageCount();
      }

      emit(state.copyWith(isSending: false));

    } catch (e) {
      emit(state.copyWith(
        isSending: false,
        errorMessage: e.toString().replaceFirst('Exception: ', ''),
      ));
    }
  }

  void _onMessageReceived(
    ChatMessageReceived event,
    Emitter<ChatState> emit,
  ) {
    // This is now handled by the real-time stream in _onMessagesLoaded
    // No need to manually add messages as they come through the stream
  }

  Future<void> _onMessageSaved(
    ChatMessageSaved event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final messageIndex = state.messages.indexWhere(
        (m) => m.id == event.messageId,
      );

      if (messageIndex != -1) {
        final message = state.messages[messageIndex];
        final newSavedStatus = !message.isSaved;

        // Update local state first for immediate UI feedback
        final updatedMessages = [...state.messages];
        updatedMessages[messageIndex] = message.copyWith(
          isSaved: newSavedStatus,
        );

        emit(state.copyWith(messages: updatedMessages));

        // Persist to Firebase
        await _chatService.saveMessage(
          message.conversationId,
          message.id,
          newSavedStatus,
        );
      }
    } catch (e) {
      Logger.error('Error saving message', tag: 'ChatBloc', error: e);

      // Revert the local state if Firebase update fails
      final messageIndex = state.messages.indexWhere(
        (m) => m.id == event.messageId,
      );

      if (messageIndex != -1) {
        final updatedMessages = [...state.messages];
        updatedMessages[messageIndex] = updatedMessages[messageIndex].copyWith(
          isSaved: !updatedMessages[messageIndex].isSaved,
        );
        emit(state.copyWith(
          messages: updatedMessages,
          errorMessage: 'Failed to save message: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onCompanionChanged(
    ChatCompanionChanged event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final companions = _storageService.getCompanions();
      final companion = companions.firstWhere(
        (c) => c.id == event.companionId,
      );

      await _storageService.saveSelectedCompanion(companion);

      emit(state.copyWith(
        selectedCompanion: companion,
        currentConversationId: companion.conversationId,
        messages: [], // Clear messages when switching companions
      ));

      // Load messages for new companion
      add(ChatMessagesLoaded(companion.conversationId));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to change companion'));
    }
  }

  Future<void> _onRelationshipScoreUpdated(
    ChatRelationshipScoreUpdated event,
    Emitter<ChatState> emit,
  ) async {
    try {
      Logger.info('Processing relationship score update: ${event.previousScore} -> ${event.newScore}', tag: 'ChatBloc');

      // Update the companion with new relationship data
      if (state.selectedCompanion != null) {
        // Store insights if available
        if (event.insights != null && event.insights!.isNotEmpty) {
          Logger.info('Storing ${event.insights!.length} insights for companion ${state.selectedCompanion!.id}', tag: 'ChatBloc');
          print('ChatBloc: Companion ID for insights storage: ${state.selectedCompanion!.id}');
          print('ChatBloc: Companion name: ${state.selectedCompanion!.name}');
          await _insightsService.storeInsights(
            state.selectedCompanion!.id,
            event.insights!,
          );
          Logger.info('Successfully stored insights', tag: 'ChatBloc');
        } else {
          Logger.warning('No insights to store', tag: 'ChatBloc');
        }

        // Get updated companion data from Firebase to get the latest relationship info
        final companions = await _chatService.firebaseService.getUserCompanions(
          state.selectedCompanion!.userId,
        );

        final updatedCompanion = companions.firstWhere(
          (c) => c.id == state.selectedCompanion!.id,
          orElse: () => state.selectedCompanion!,
        );

        // Update local storage with the new companion data
        await _storageService.saveSelectedCompanion(updatedCompanion);

        // Emit state with updated companion and score change info
        emit(state.copyWith(
          selectedCompanion: updatedCompanion,
          lastScoreChange: event.scoreChange,
          isScoreUpdating: true,
        ));

        // Clear the score updating flag after a short delay for animation
        await Future.delayed(const Duration(milliseconds: 2000));
        emit(state.copyWith(
          isScoreUpdating: false,
          lastScoreChange: null,
        ));

        Logger.info('Relationship score update completed', tag: 'ChatBloc');
      } else {
        Logger.warning('No selected companion to update', tag: 'ChatBloc');
      }
    } catch (e) {
      Logger.error('Error updating relationship score', tag: 'ChatBloc', error: e);
    }
  }

  void _onReset(
    ChatReset event,
    Emitter<ChatState> emit,
  ) {
    emit(const ChatState());
  }



  Future<void> _onScenarioActivated(
    ChatScenarioActivated event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _storageService.saveActiveScenario(event.scenarioId);
      emit(state.copyWith(
        activeScenarioId: event.scenarioId,
        activeGameId: null, // Clear active game
      ));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to activate scenario'));
    }
  }

  Future<void> _onGameActivated(
    ChatGameActivated event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _storageService.saveActiveGame(event.gameId);
      emit(state.copyWith(
        activeGameId: event.gameId,
        activeScenarioId: null, // Clear active scenario
      ));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to activate game'));
    }
  }

  Future<void> _onScenarioGameCancelled(
    ChatScenarioGameCancelled event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _storageService.clearActiveScenarioGame();
      emit(state.copyWith(
        activeScenarioId: null,
        activeGameId: null,
      ));
    } catch (e) {
      emit(state.copyWith(errorMessage: 'Failed to cancel scenario/game'));
    }
  }

}
