import 'package:equatable/equatable.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../shared/models/companion_model.dart';

const _undefined = Object();

class ChatState extends Equatable {
  final List<MessageModel> messages;
  final bool isLoading;
  final bool isSending;
  final String? errorMessage;
  final CompanionModel? selectedCompanion;
  final String? currentConversationId;
  final String? activeScenarioId;
  final String? activeGameId;
  final int? lastScoreChange;
  final bool isScoreUpdating;

  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.isSending = false,
    this.errorMessage,
    this.selectedCompanion,
    this.currentConversationId,
    this.activeScenarioId,
    this.activeGameId,
    this.lastScoreChange,
    this.isScoreUpdating = false,
  });

  ChatState copyWith({
    List<MessageModel>? messages,
    bool? isLoading,
    bool? isSending,
    String? errorMessage,
    CompanionModel? selectedCompanion,
    String? currentConversationId,
    Object? activeScenarioId = _undefined,
    Object? activeGameId = _undefined,
    Object? lastScoreChange = _undefined,
    bool? isScoreUpdating,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isSending: isSending ?? this.isSending,
      errorMessage: errorMessage,
      selectedCompanion: selectedCompanion ?? this.selectedCompanion,
      currentConversationId: currentConversationId ?? this.currentConversationId,
      activeScenarioId: activeScenarioId == _undefined ? this.activeScenarioId : activeScenarioId as String?,
      activeGameId: activeGameId == _undefined ? this.activeGameId : activeGameId as String?,
      lastScoreChange: lastScoreChange == _undefined ? this.lastScoreChange : lastScoreChange as int?,
      isScoreUpdating: isScoreUpdating ?? this.isScoreUpdating,
    );
  }

  @override
  List<Object?> get props => [
        messages,
        isLoading,
        isSending,
        errorMessage,
        selectedCompanion,
        currentConversationId,
        activeScenarioId,
        activeGameId,
        lastScoreChange,
        isScoreUpdating,
      ];
}
