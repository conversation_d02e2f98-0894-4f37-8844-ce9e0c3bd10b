import 'dart:io';
import 'package:equatable/equatable.dart';
import '../../../../shared/models/relationship_insight_model.dart';

abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object?> get props => [];
}

class ChatMessagesLoaded extends ChatEvent {
  final String conversationId;

  const ChatMessagesLoaded(this.conversationId);

  @override
  List<Object?> get props => [conversationId];
}

class ChatMessageSent extends ChatEvent {
  final String message;
  final String? scenarioId;
  final String? gameId;
  final File? imageFile;

  const ChatMessageSent(
    this.message, {
    this.scenarioId,
    this.gameId,
    this.imageFile,
  });

  @override
  List<Object?> get props => [message, scenarioId, gameId, imageFile];
}

class ChatMessageReceived extends ChatEvent {
  final String message;

  const ChatMessageReceived(this.message);

  @override
  List<Object?> get props => [message];
}

class ChatMessageSaved extends ChatEvent {
  final String messageId;

  const ChatMessageSaved(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

class ChatCompanionChanged extends ChatEvent {
  final String companionId;

  const ChatCompanionChanged(this.companionId);

  @override
  List<Object?> get props => [companionId];
}

class ChatReset extends ChatEvent {}

class ChatScenarioActivated extends ChatEvent {
  final String scenarioId;

  const ChatScenarioActivated(this.scenarioId);

  @override
  List<Object?> get props => [scenarioId];
}

class ChatGameActivated extends ChatEvent {
  final String gameId;

  const ChatGameActivated(this.gameId);

  @override
  List<Object?> get props => [gameId];
}

class ChatScenarioGameCancelled extends ChatEvent {}

class ChatRelationshipScoreUpdated extends ChatEvent {
  final int previousScore;
  final int newScore;
  final int scoreChange;
  final String? description;
  final List<RelationshipInsightModel>? insights;

  const ChatRelationshipScoreUpdated({
    required this.previousScore,
    required this.newScore,
    required this.scoreChange,
    this.description,
    this.insights,
  });

  @override
  List<Object?> get props => [previousScore, newScore, scoreChange, description, insights];
}
