import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';

import '../../../../shared/models/subscription_tier.dart';
import '../../../premium/presentation/bloc/premium_bloc.dart';
import '../../../premium/presentation/bloc/premium_state.dart';

class ScenariosGamesModal extends StatefulWidget {
  final Function(String) onScenarioSelected;
  final Function(String) onGameSelected;

  const ScenariosGamesModal({
    super.key,
    required this.onScenarioSelected,
    required this.onGameSelected,
  });

  @override
  State<ScenariosGamesModal> createState() => _ScenariosGamesModalState();
}

class _ScenariosGamesModalState extends State<ScenariosGamesModal>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.secondaryTextColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: AppTheme.messageGradient,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: const EdgeInsets.all(2),
              labelColor: Theme.of(context).colorScheme.onPrimary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 14,
              ),
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(
                  height: 44,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.casino, size: 18),
                      SizedBox(width: 8),
                      Text('Scenarios'),
                    ],
                  ),
                ),
                Tab(
                  height: 44,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.sports_esports, size: 18),
                      SizedBox(width: 8),
                      Text('Games'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildScenariosTab(),
                _buildGamesTab(),
              ],
            ),
          ),
          
          // Close Button
          Padding(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                ),
                child: const Text('Close'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScenariosTab() {
    return BlocBuilder<PremiumBloc, PremiumState>(
      builder: (context, state) {
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          itemCount: AppConstants.scenarios.length,
          itemBuilder: (context, index) {
            final scenario = AppConstants.scenarios[index];
            final scenarioId = scenario['id'] as String;
            final hasAccess = _hasScenarioAccess(state.currentTier, scenarioId);

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: GestureDetector(
                onTap: hasAccess ? () {
                  Navigator.pop(context);
                  widget.onScenarioSelected(scenarioId);
                } : null,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: hasAccess ? [
                        Color(scenario['color'] as int),
                        Color(scenario['color'] as int).withValues(alpha: 0.7),
                      ] : [
                        Colors.grey.withValues(alpha: 0.5),
                        Colors.grey.withValues(alpha: 0.3),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: hasAccess ? [
                      BoxShadow(
                        color: Color(scenario['color'] as int).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ] : [],
                  ),
                  child: Stack(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: hasAccess ? 0.2 : 0.1),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: Center(
                              child: Text(
                                scenario['icon'] as String,
                                style: TextStyle(
                                  fontSize: 24,
                                  color: hasAccess ? Colors.white : Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  scenario['title'] as String,
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: hasAccess ? Colors.white : Colors.grey,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  scenario['description'] as String,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: hasAccess ? Colors.white.withValues(alpha: 0.9) : Colors.grey.withValues(alpha: 0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            hasAccess ? Icons.arrow_forward_ios : Icons.lock,
                            color: hasAccess ? Colors.white : Colors.grey,
                            size: 20,
                          ),
                        ],
                      ),
                      if (!hasAccess)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getRequiredTierForScenario(scenarioId).index >= SubscriptionTier.pro.index
                                  ? const Color(0xFFC0C0C0)
                                  : const Color(0xFFCD7F32),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getRequiredTierForScenario(scenarioId).displayName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildGamesTab() {
    return BlocBuilder<PremiumBloc, PremiumState>(
      builder: (context, state) {
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          itemCount: AppConstants.games.length,
          itemBuilder: (context, index) {
            final game = AppConstants.games[index];
            final gameId = game['id'] as String;
            final hasAccess = _hasGameAccess(state.currentTier, gameId);

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: GestureDetector(
                onTap: hasAccess ? () {
                  Navigator.pop(context);
                  widget.onGameSelected(gameId);
                } : null,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: hasAccess ? [
                        Color(game['color'] as int),
                        Color(game['color'] as int).withValues(alpha: 0.7),
                      ] : [
                        Colors.grey.withValues(alpha: 0.5),
                        Colors.grey.withValues(alpha: 0.3),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: hasAccess ? [
                      BoxShadow(
                        color: Color(game['color'] as int).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ] : [],
                  ),
                  child: Stack(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: hasAccess ? 0.2 : 0.1),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: Center(
                              child: Text(
                                game['icon'] as String,
                                style: TextStyle(
                                  fontSize: 24,
                                  color: hasAccess ? Colors.white : Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  game['title'] as String,
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: hasAccess ? Colors.white : Colors.grey,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  game['description'] as String,
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: hasAccess ? Colors.white.withValues(alpha: 0.9) : Colors.grey.withValues(alpha: 0.7),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            hasAccess ? Icons.arrow_forward_ios : Icons.lock,
                            color: hasAccess ? Colors.white : Colors.grey,
                            size: 20,
                          ),
                        ],
                      ),
                      if (!hasAccess)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFFC0C0C0), // Silver for Pro
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Pro',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  bool _hasScenarioAccess(SubscriptionTier currentTier, String scenarioId) {
    final limits = SubscriptionLimits.getLimits(currentTier);
    return limits.allowedScenarios.contains(scenarioId);
  }

  bool _hasGameAccess(SubscriptionTier currentTier, String gameId) {
    final limits = SubscriptionLimits.getLimits(currentTier);
    return limits.allowedGames.contains(gameId);
  }

  SubscriptionTier _getRequiredTierForScenario(String scenarioId) {
    switch (scenarioId) {
      case 'comfort_me':
      case 'mental_health_checkin':
        return SubscriptionTier.plus;
      default:
        return SubscriptionTier.pro;
    }
  }


}
