import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/companion_model.dart';
import '../../../../shared/widgets/companion_avatar.dart';
import '../../../companion/presentation/bloc/companion_bloc.dart';
import '../../../companion/presentation/bloc/companion_event.dart';
import '../../../companion/presentation/bloc/companion_state.dart';
import '../../../companion/presentation/pages/companion_setup_page.dart';

class CompanionSelectorDropdown extends StatelessWidget {
  final CompanionModel? selectedCompanion;
  final Function(String) onCompanionChanged;

  const CompanionSelectorDropdown({
    super.key,
    this.selectedCompanion,
    required this.onCompanionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CompanionBloc, CompanionState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () => _showCompanionSelector(context, state.companions),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Companion Avatar
                CompanionAvatarMedium(
                  imageUrl: selectedCompanion?.imageUrl,
                ),
                
                const SizedBox(width: 12),
                
                // Companion Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedCompanion?.name ?? 'Select Companion',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      if (selectedCompanion != null) ...[
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: selectedCompanion!.connectionType == 'romantic'
                                    ? AppTheme.romanticColor.withValues(alpha: 0.2)
                                    : AppTheme.friendlyColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                selectedCompanion!.connectionType == 'romantic'
                                    ? '💕 ${selectedCompanion!.relationshipStatus}'
                                    : '😊 ${selectedCompanion!.relationshipStatus}',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: selectedCompanion!.connectionType == 'romantic'
                                      ? AppTheme.romanticColor
                                      : AppTheme.friendlyColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Relationship Progress
                if (selectedCompanion != null) ...[
                  Column(
                    children: [
                      Text(
                        'Cold',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 60,
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: selectedCompanion!.relationshipScore / 100,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Color(0xFF4FC3F7),
                                  Color(0xFFFF8A65),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Hot',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                ],
                
                // Dropdown Icon
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showCompanionSelector(BuildContext context, List<CompanionModel> companions) {
    // Calculate dynamic height based on number of companions
    final double baseHeight = 200; // Header + padding
    final double companionItemHeight = 60; // Reduced from ~76
    final double addCompanionHeight = 60;
    final double maxHeight = MediaQuery.of(context).size.height * 0.8;

    final double calculatedHeight = baseHeight +
        (companions.length * companionItemHeight) +
        addCompanionHeight;

    final double sheetHeight = calculatedHeight > maxHeight ? maxHeight : calculatedHeight;

    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      constraints: BoxConstraints(
        maxHeight: sheetHeight,
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16), // Reduced from 24
            Text(
              'Your Companions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16), // Reduced from 24

            // Companions List - Make scrollable if needed
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: companions.map((companion) => _buildCompanionItem(
                    context,
                    companion,
                    companion.id == selectedCompanion?.id,
                  )).toList(),
                ),
              ),
            ),

            const SizedBox(height: 12), // Reduced from 16
            
            // Add New Companion
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
                _navigateToCompanionSetup(context);
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12), // Reduced from 16
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32, // Reduced from 40
                      height: 32, // Reduced from 40
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                      ),
                      child: Icon(
                        Icons.add,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20, // Reduced from 24
                      ),
                    ),
                    const SizedBox(width: 10), // Reduced from 12
                    Text(
                      'Add New Companion',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith( // Changed from titleMedium
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanionItem(
    BuildContext context,
    CompanionModel companion,
    bool isSelected,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8), // Reduced from 12
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
          onCompanionChanged(companion.id);
        },
        child: Container(
          padding: const EdgeInsets.all(12), // Reduced from 16
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: Row(
            children: [
              CompanionAvatarSmall(
                imageUrl: companion.imageUrl,
                isActive: isSelected,
              ),
              const SizedBox(width: 10), // Reduced from 12
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min, // Added to reduce height
                  children: [
                    Text(
                      companion.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith( // Changed from titleMedium
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2), // Added small spacing
                    Text(
                      companion.relationshipLevelName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20, // Reduced from 24
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCompanionSetup(BuildContext context) async {
    // Navigate to companion setup page (from chat screen context)
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CompanionSetupPage(isFromSettings: false),
      ),
    );

    // If companion was created successfully, refresh the companion list
    if (result == true && context.mounted) {
      // Trigger companion list reload
      context.read<CompanionBloc>().add(CompanionsLoaded());
    }
  }
}
