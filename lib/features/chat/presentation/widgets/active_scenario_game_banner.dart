import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

class ActiveScenarioGameBanner extends StatelessWidget {
  final String? activeScenarioId;
  final String? activeGameId;
  final VoidCallback onCancel;

  const ActiveScenarioGameBanner({
    super.key,
    this.activeScenarioId,
    this.activeGameId,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    // Don't show banner if no active scenario or game
    if (activeScenarioId == null && activeGameId == null) {
      return const SizedBox.shrink();
    }

    // Get the active item details
    Map<String, dynamic>? activeItem;
    String type = '';
    
    if (activeScenarioId != null) {
      activeItem = AppConstants.scenarios.firstWhere(
        (scenario) => scenario['id'] == activeScenarioId,
        orElse: () => {},
      );
      type = 'Scenario';
    } else if (activeGameId != null) {
      activeItem = AppConstants.games.firstWhere(
        (game) => game['id'] == activeGameId,
        orElse: () => {},
      );
      type = 'Game';
    }

    if (activeItem == null || activeItem.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(activeItem['color'] as int),
            Color(activeItem['color'] as int).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Color(activeItem['color'] as int).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  activeItem['icon'] as String,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '$type Active',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    activeItem['title'] as String,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Cancel button
            GestureDetector(
              onTap: onCancel,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
