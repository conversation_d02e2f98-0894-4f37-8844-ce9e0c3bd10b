import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/message_model.dart';
import '../../../../shared/models/companion_model.dart';
import '../../../../shared/widgets/companion_avatar.dart';

class MessageBubble extends StatelessWidget {
  final MessageModel message;
  final VoidCallback onSave;
  final CompanionModel? companion;

  const MessageBubble({
    super.key,
    required this.message,
    required this.onSave,
    this.companion,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.isFromUser;
    final timeFormat = DateFormat('HH:mm');

    return Row(
      mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isUser) ...[
          // AI Avatar
          CompanionAvatar(
            imageUrl: companion?.imageUrl,
            size: 32,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            iconColor: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
        ],
        
        // Message Content
        Flexible(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            child: Column(
              crossAxisAlignment: isUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                // Message Bubble
                GestureDetector(
                  onLongPress: () => _showMessageOptions(context),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      gradient: isUser
                          ? LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.primary.withOpacity(0.8),
                              ],
                            )
                          : null,
                      color: isUser
                          ? null
                          : Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF2A2A2A) // Dark gray for AI messages in dark theme
                              : const Color(0xFFF1F3F4), // Light gray for AI messages in light theme
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(20),
                        topRight: const Radius.circular(20),
                        bottomLeft: Radius.circular(isUser ? 20 : 4),
                        bottomRight: Radius.circular(isUser ? 4 : 20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Image if present
                        if (message.hasImage) ...[
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              message.imageUrl!,
                              width: 200,
                              height: 150,
                              fit: BoxFit.cover,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  width: 200,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).scaffoldBackgroundColor,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 200,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    color: AppTheme.backgroundColor,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.broken_image,
                                      color: AppTheme.hintTextColor,
                                      size: 32,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          if (message.content.isNotEmpty) const SizedBox(height: 8),
                        ],

                        // Text content if present
                        if (message.content.isNotEmpty)
                          Text(
                            message.content,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: isUser
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurface,
                              height: 1.4,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Timestamp and Save Status
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (message.isSaved)
                      Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: Icon(
                          Icons.bookmark,
                          size: 12,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    Text(
                      timeFormat.format(message.timestamp),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        if (isUser) ...[
          const SizedBox(width: 8),
          // User Avatar
          CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
            child: Icon(
              Icons.person,
              color: Theme.of(context).colorScheme.primary,
              size: 18,
            ),
          ),
        ],
      ],
    );
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            
            // Save/Unsave Option
            ListTile(
              leading: Icon(
                message.isSaved ? Icons.bookmark_remove : Icons.bookmark_add,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: Text(
                message.isSaved ? 'Remove from Saved' : 'Save Message',
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
              ),
              onTap: () {
                Navigator.pop(context);
                onSave();
              },
            ),
            
            // Copy Option
            ListTile(
              leading: const Icon(
                Icons.copy,
                color: AppTheme.secondaryTextColor,
              ),
              title: const Text(
                'Copy Message',
                style: TextStyle(color: AppTheme.primaryTextColor),
              ),
              onTap: () async {
                Navigator.pop(context);
                await Clipboard.setData(ClipboardData(text: message.content));
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Message copied to clipboard'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                }
              },
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
