import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/widgets/subscription_gate.dart';
import '../../../premium/presentation/bloc/premium_bloc.dart';
import '../../../premium/presentation/bloc/premium_state.dart';
import '../bloc/chat_bloc.dart';
import '../bloc/chat_event.dart';
import '../bloc/chat_state.dart';
import '../widgets/companion_selector_dropdown.dart';
import '../widgets/message_bubble.dart';
import '../widgets/chat_input_field.dart';
import '../widgets/scenarios_games_modal.dart';
import '../widgets/selected_image_preview.dart';
import '../widgets/active_scenario_game_banner.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  File? _selectedImage;

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _sendMessage(String message, {String? scenarioId, String? gameId, File? imageFile}) {
    final finalImageFile = imageFile ?? _selectedImage;

    // Debug logging
    Logger.info('_sendMessage called with message: "$message", scenarioId: $scenarioId, gameId: $gameId', tag: 'ChatPage');

    // Allow empty messages for scenario/game selection or when there's an image
    bool isScenarioOrGame = scenarioId != null || gameId != null;
    bool hasContent = message.trim().isNotEmpty || finalImageFile != null || isScenarioOrGame;

    Logger.info('isScenarioOrGame: $isScenarioOrGame, hasContent: $hasContent', tag: 'ChatPage');

    if (hasContent) {
      Logger.info('Sending ChatMessageSent event with message: "${message.trim()}"', tag: 'ChatPage');
      context.read<ChatBloc>().add(
            ChatMessageSent(
              message.trim(),
              scenarioId: scenarioId,
              gameId: gameId,
              imageFile: finalImageFile,
            ),
          );
      _messageController.clear();
      setState(() {
        _selectedImage = null; // Clear selected image after sending
      });

      // Scroll to bottom after sending message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  void _showScenariosGamesModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ScenariosGamesModal(
        onScenarioSelected: (scenarioId) {
          // Activate scenario and send activation marker
          Logger.info('Scenario selected: $scenarioId, sending activation marker', tag: 'ChatPage');
          context.read<ChatBloc>().add(ChatScenarioActivated(scenarioId));
          _sendMessage("__SCENARIO_ACTIVATION__", scenarioId: scenarioId);
        },
        onGameSelected: (gameId) {
          // Activate game and send activation marker
          Logger.info('Game selected: $gameId, sending activation marker', tag: 'ChatPage');
          context.read<ChatBloc>().add(ChatGameActivated(gameId));
          _sendMessage("__GAME_ACTIVATION__", gameId: gameId);
        },
      ),
    );
  }

  void _onImageSelected(File imageFile) {
    setState(() {
      _selectedImage = imageFile;
    });
  }

  void _clearSelectedImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: BlocListener<ChatBloc, ChatState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
          
          // Scroll to bottom when new messages arrive
          if (state.messages.isNotEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToBottom();
            });
          }
        },
        child: Column(
          children: [
            // Header with companion selector
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: BlocBuilder<ChatBloc, ChatState>(
                    builder: (context, state) {
                      return CompanionSelectorDropdown(
                        selectedCompanion: state.selectedCompanion,
                        onCompanionChanged: (companionId) {
                          context.read<ChatBloc>().add(
                                ChatCompanionChanged(companionId),
                              );
                        },
                      );
                    },
                  ),
                ),
              ),
            ),

            // Active scenario/game banner
            BlocBuilder<ChatBloc, ChatState>(
              builder: (context, state) {
                return ActiveScenarioGameBanner(
                  activeScenarioId: state.activeScenarioId,
                  activeGameId: state.activeGameId,
                  onCancel: () {
                    context.read<ChatBloc>().add(ChatScenarioGameCancelled());
                  },
                );
              },
            ),

            // Usage indicators
            BlocBuilder<PremiumBloc, PremiumState>(
              builder: (context, state) {
                if (state.usageStatus == null) return const SizedBox.shrink();

                final usage = state.usageStatus!;
                return Column(
                  children: [
                    if (!usage.canSendMessage && usage.remainingMessages >= 0)
                      UsageLimitWidget(
                        limitType: 'Messages',
                        remaining: usage.remainingMessages,
                        total: usage.remainingMessages + 1, // Approximate total
                      ),
                    if (!usage.canSendImage && usage.remainingImages > 0)
                      UsageLimitWidget(
                        limitType: 'Images',
                        remaining: usage.remainingImages,
                        total: usage.remainingImages + 1, // Approximate total
                      ),
                  ],
                );
              },
            ),

            // Messages list
            Expanded(
              child: BlocBuilder<ChatBloc, ChatState>(
                builder: (context, state) {
                  if (state.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.userMessageColor,
                        ),
                      ),
                    );
                  }

                  if (state.messages.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.chat_bubble_outline,
                            size: 64,
                            color: AppTheme.secondaryTextColor.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Start a conversation',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Send a message to begin chatting with your companion',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.hintTextColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: state.messages.length,
                    itemBuilder: (context, index) {
                      final message = state.messages[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: MessageBubble(
                          message: message,
                          companion: state.selectedCompanion,
                          onSave: () {
                            context.read<ChatBloc>().add(
                                  ChatMessageSaved(message.id),
                                );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            
            // Chat input
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  // BoxShadow(
                  //   color: Colors.black12,
                  //   blurRadius: 4,
                  //   offset: const Offset(0, -2),
                  // ),
                ],
              ),
              child: SafeArea(
                top: false,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Selected image preview
                    if (_selectedImage != null)
                      SelectedImagePreview(
                        imageFile: _selectedImage!,
                        onRemove: _clearSelectedImage,
                      ),

                    // Chat input field
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: BlocBuilder<ChatBloc, ChatState>(
                        builder: (context, state) {
                          return ChatInputField(
                            controller: _messageController,
                            onSend: _sendMessage,
                            onImageSelected: _onImageSelected,
                            onShowScenariosGames: _showScenariosGamesModal,
                            isEnabled: !state.isSending && state.selectedCompanion != null,
                            isSending: state.isSending,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
