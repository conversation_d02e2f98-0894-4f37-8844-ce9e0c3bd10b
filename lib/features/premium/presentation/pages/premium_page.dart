import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';

import '../../../../shared/models/subscription_tier.dart';
import '../bloc/premium_bloc.dart';
import '../bloc/premium_event.dart';
import '../bloc/premium_state.dart';

class PremiumPage extends StatefulWidget {
  const PremiumPage({super.key});

  @override
  State<PremiumPage> createState() => _PremiumPageState();
}

class _PremiumPageState extends State<PremiumPage> {
  @override
  void initState() {
    super.initState();
    context.read<PremiumBloc>().add(PremiumStatusLoaded());
    context.read<PremiumBloc>().add(PremiumFeaturesLoaded());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'Nova Soul Premium',
                style: TextStyle(
                  color: Theme.of(context).appBarTheme.foregroundColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primary.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.workspace_premium,
                    size: 80,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: BlocListener<PremiumBloc, PremiumState>(
              listener: (context, state) {
                if (state.errorMessage != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.errorMessage!),
                      backgroundColor: Theme.of(context).colorScheme.error,
                      action: SnackBarAction(
                        label: 'Dismiss',
                        textColor: Colors.white,
                        onPressed: () {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        },
                      ),
                    ),
                  );
                } else if (!state.isLoading && state.isPremium && state.currentTier != SubscriptionTier.free) {
                  // Show success message when subscription is active and not loading
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Welcome to ${state.currentTier.displayName}! 🎉'),
                      backgroundColor: AppTheme.successColor,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Premium Benefits
                  Text(
                    'Unlock Premium Features',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Enhance your AI companion experience with exclusive features',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Features List
                  ..._buildFeaturesList(context),

                  const SizedBox(height: 32),

                  // Current Tier Status
                  BlocBuilder<PremiumBloc, PremiumState>(
                    builder: (context, state) {
                      return _buildCurrentTierStatus(context, state);
                    },
                  ),

                  const SizedBox(height: 32),

                  // Subscription Tiers
                  Text(
                    'Choose Your Plan',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 16),

                  BlocBuilder<PremiumBloc, PremiumState>(
                    builder: (context, state) {
                      return Column(
                        children: [
                          _buildTierCard(context, SubscriptionTier.free, state),
                          const SizedBox(height: 16),
                          _buildTierCard(context, SubscriptionTier.plus, state),
                          const SizedBox(height: 16),
                          _buildTierCard(context, SubscriptionTier.pro, state),
                          const SizedBox(height: 16),
                          _buildTierCard(context, SubscriptionTier.elite, state),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Restore Purchases Button
                  BlocBuilder<PremiumBloc, PremiumState>(
                    builder: (context, state) {
                      return TextButton(
                        onPressed: state.isLoading ? null : () {
                          context.read<PremiumBloc>().add(PremiumRestorePurchases());
                        },
                        child: Text(
                          state.isLoading ? 'Restoring...' : 'Restore Purchases',
                          style: const TextStyle(
                            color: AppTheme.userMessageColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 32),

                  // Terms
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Premium Terms',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Cancel anytime\n• 7-day free trial for new users\n• All features included\n• Priority customer support',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          ), // Close SliverToBoxAdapter
        ],
      ),
    );
  }

  List<Widget> _buildFeaturesList(BuildContext context) {
    final features = [
      {
        'icon': Icons.chat_bubble,
        'title': 'Unlimited Messages',
        'description': 'Chat without limits - no daily message restrictions',
        'color': AppTheme.userMessageColor,
      },
      {
        'icon': Icons.psychology,
        'title': 'Advanced AI Personality',
        'description': 'More sophisticated and nuanced companion responses',
        'color': AppTheme.friendlyColor,
      },
      {
        'icon': Icons.photo_camera,
        'title': 'Photo Sharing',
        'description': 'Share and receive photos with your companion',
        'color': AppTheme.romanticColor,
      },
      {
        'icon': Icons.voice_chat,
        'title': 'Voice Messages',
        'description': 'Send and receive voice messages for deeper connection',
        'color': AppTheme.successColor,
      },
      {
        'icon': Icons.casino,
        'title': 'Exclusive Scenarios',
        'description': 'Access premium scenarios and interactive experiences',
        'color': AppTheme.warningColor,
      },
      {
        'icon': Icons.backup,
        'title': 'Cloud Backup',
        'description': 'Never lose your conversations with automatic cloud sync',
        'color': AppTheme.userMessageColor,
      },
    ];

    return features.map((feature) => _buildFeatureItem(
      context,
      feature['icon'] as IconData,
      feature['title'] as String,
      feature['description'] as String,
      feature['color'] as Color,
    )).toList();
  }

  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentTierStatus(BuildContext context, PremiumState state) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getTierColor(state.currentTier),
            _getTierColor(state.currentTier).withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Text(
            state.currentTier.emoji,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Plan: ${state.currentTier.displayName}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  state.currentTier.description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTierCard(BuildContext context, SubscriptionTier tier, PremiumState state) {
    final limits = SubscriptionLimits.getLimits(tier);
    final isCurrentTier = state.currentTier == tier;
    final canUpgrade = tier.index > state.currentTier.index;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: isCurrentTier
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getTierColor(tier),
                  _getTierColor(tier).withValues(alpha: 0.7),
                ],
              )
            : null,
        color: isCurrentTier ? null : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCurrentTier
              ? Colors.transparent
              : _getTierColor(tier).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                tier.emoji,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tier.displayName,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isCurrentTier ? Colors.white : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      tier.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: isCurrentTier
                            ? Colors.white.withValues(alpha: 0.9)
                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (isCurrentTier)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'CURRENT',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Features list
          ..._buildTierFeatures(tier, limits, isCurrentTier),

          if (canUpgrade) ...[
            const SizedBox(height: 16),
            BlocBuilder<PremiumBloc, PremiumState>(
              builder: (context, premiumState) {
                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: premiumState.isLoading ? null : () => _handleUpgrade(context, tier),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getTierColor(tier),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: premiumState.isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Upgrade to ${tier.displayName}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildTierFeatures(SubscriptionTier tier, SubscriptionLimits limits, bool isCurrentTier) {
    final features = <String>[];

    if (tier == SubscriptionTier.free) {
      features.addAll([
        'Chat with AI companion',
        '${limits.messagesPerDay} messages/day',
        '${limits.companionLimit} companion',
        'No memories saved',
        'No image sending',
        'No scenarios or games',
      ]);
    } else {
      if (limits.hasMemory) features.add('AI remembers conversations');

      if (limits.hasUnlimitedMessages) {
        features.add('Unlimited messages');
      } else {
        features.add('${limits.messagesPerDay} messages/day');
      }

      if (limits.hasUnlimitedCompanions) {
        features.add('Unlimited companions');
      } else if (limits.companionLimit == 1) {
        features.add('${limits.companionLimit} companion');
      } else {
        features.add('${limits.companionLimit} companions');
      }

      if (limits.hasProfilePic) features.add('Custom profile pictures');

      if (limits.hasUnlimitedImages) {
        features.add('Unlimited image sending');
      } else if (limits.imagesPerDay > 0) {
        features.add('${limits.imagesPerDay} image/day');
      }

      if (limits.allowedScenarios.isNotEmpty) {
        if (limits.allowedScenarios.length == 2) {
          features.add('2 exclusive scenarios');
        } else {
          features.add('All scenarios & games');
        }
      }
    }

    return features.map((feature) => Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: isCurrentTier ? Colors.white : _getTierColor(tier),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                color: isCurrentTier
                    ? Colors.white.withValues(alpha: 0.9)
                    : Theme.of(context).colorScheme.onSurface,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Color _getTierColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return Colors.grey;
      case SubscriptionTier.plus:
        return const Color(0xFFCD7F32); // Bronze
      case SubscriptionTier.pro:
        return const Color(0xFFC0C0C0); // Silver
      case SubscriptionTier.elite:
        return const Color(0xFFFFD700); // Gold
    }
  }

  void _handleUpgrade(BuildContext context, SubscriptionTier tier) {
    final productId = _getProductIdForTier(tier);
    context.read<PremiumBloc>().add(PremiumSubscriptionRequested(productId));
  }

  String _getProductIdForTier(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.plus:
        return 'nova_soul_plus_monthly';
      case SubscriptionTier.pro:
        return 'nova_soul_pro_monthly';
      case SubscriptionTier.elite:
        return 'nova_soul_elite_monthly';
      default:
        return '';
    }
  }
}
