import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/subscription_service.dart';
import '../../../../core/services/usage_tracking_service.dart';
import '../../../../shared/models/subscription_tier.dart';
import 'premium_event.dart';
import 'premium_state.dart';

class PremiumBloc extends Bloc<PremiumEvent, PremiumState> {
  final StorageService _storageService;
  final SubscriptionService _subscriptionService;
  final UsageTrackingService _usageTrackingService;

  PremiumBloc({
    required StorageService storageService,
    required SubscriptionService subscriptionService,
    required UsageTrackingService usageTrackingService,
  })  : _storageService = storageService,
        _subscriptionService = subscriptionService,
        _usageTrackingService = usageTrackingService,
        super(const PremiumState()) {
    on<PremiumStatusLoaded>(_onPremiumStatusLoaded);
    on<PremiumSubscriptionRequested>(_onSubscriptionRequested);
    on<PremiumSubscriptionCancelled>(_onSubscriptionCancelled);
    on<PremiumFeaturesLoaded>(_onFeaturesLoaded);
    on<PremiumRestorePurchases>(_onRestorePurchases);
  }

  Future<void> _onPremiumStatusLoaded(
    PremiumStatusLoaded event,
    Emitter<PremiumState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final currentTier = await _subscriptionService.getCurrentTier();
      final plans = await _subscriptionService.getAvailablePlans();
      final usageStatus = await _usageTrackingService.getUsageStatus();

      emit(state.copyWith(
        isLoading: false,
        isPremium: currentTier != SubscriptionTier.free,
        currentPlan: currentTier.name,
        plans: plans,
        currentTier: currentTier,
        usageStatus: usageStatus,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load premium status',
      ));
    }
  }

  Future<void> _onSubscriptionRequested(
    PremiumSubscriptionRequested event,
    Emitter<PremiumState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final success = await _subscriptionService.purchaseSubscription(event.planId);

      if (success) {
        final currentTier = await _subscriptionService.getCurrentTier();
        final usageStatus = await _usageTrackingService.getUsageStatus();

        emit(state.copyWith(
          isLoading: false,
          isPremium: currentTier != SubscriptionTier.free,
          currentPlan: currentTier.name,
          currentTier: currentTier,
          usageStatus: usageStatus,
          subscriptionEndDate: DateTime.now().add(const Duration(days: 30)),
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to process subscription',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to process subscription: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSubscriptionCancelled(
    PremiumSubscriptionCancelled event,
    Emitter<PremiumState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Note: Actual cancellation would be handled through App Store/Play Store
      // This just resets local state
      await _storageService.setSetting('subscription_tier', SubscriptionTier.free.name);

      emit(state.copyWith(
        isLoading: false,
        isPremium: false,
        currentPlan: SubscriptionTier.free.name,
        currentTier: SubscriptionTier.free,
        subscriptionEndDate: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to cancel subscription',
      ));
    }
  }

  Future<void> _onFeaturesLoaded(
    PremiumFeaturesLoaded event,
    Emitter<PremiumState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final features = _getPremiumFeatures();

      emit(state.copyWith(
        isLoading: false,
        features: features,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load features',
      ));
    }
  }

  Future<void> _onRestorePurchases(
    PremiumRestorePurchases event,
    Emitter<PremiumState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final success = await _subscriptionService.restorePurchases();

      if (success) {
        final currentTier = await _subscriptionService.getCurrentTier();
        final usageStatus = await _usageTrackingService.getUsageStatus();

        emit(state.copyWith(
          isLoading: false,
          isPremium: currentTier != SubscriptionTier.free,
          currentPlan: currentTier.name,
          currentTier: currentTier,
          usageStatus: usageStatus,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'No purchases to restore',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to restore purchases: ${e.toString()}',
      ));
    }
  }

  List<PremiumFeature> _getPremiumFeatures() {
    return [
      const PremiumFeature(
        id: 'unlimited_messages',
        title: 'Unlimited Messages',
        description: 'Chat without limits - no daily message restrictions',
        iconName: 'chat_bubble',
      ),
      const PremiumFeature(
        id: 'advanced_ai',
        title: 'Advanced AI Personality',
        description: 'More sophisticated and nuanced companion responses',
        iconName: 'psychology',
      ),
      const PremiumFeature(
        id: 'photo_sharing',
        title: 'Photo Sharing',
        description: 'Share and receive photos with your companion',
        iconName: 'photo_camera',
      ),
      const PremiumFeature(
        id: 'voice_messages',
        title: 'Voice Messages',
        description: 'Send and receive voice messages for deeper connection',
        iconName: 'voice_chat',
      ),
      const PremiumFeature(
        id: 'exclusive_scenarios',
        title: 'Exclusive Scenarios',
        description: 'Access premium scenarios and interactive experiences',
        iconName: 'casino',
      ),
      const PremiumFeature(
        id: 'cloud_backup',
        title: 'Cloud Backup',
        description: 'Never lose your conversations with automatic cloud sync',
        iconName: 'backup',
      ),
    ];
  }
}
