import 'package:equatable/equatable.dart';
import '../../../../shared/models/subscription_tier.dart';
import '../../../../shared/models/usage_tracking.dart';

class PremiumState extends Equatable {
  final bool isPremium;
  final String? currentPlan;
  final DateTime? subscriptionEndDate;
  final List<PremiumFeature> features;
  final List<SubscriptionPlan> plans;
  final bool isLoading;
  final String? errorMessage;
  final SubscriptionTier currentTier;
  final UsageStatus? usageStatus;

  const PremiumState({
    this.isPremium = false,
    this.currentPlan,
    this.subscriptionEndDate,
    this.features = const [],
    this.plans = const [],
    this.isLoading = false,
    this.errorMessage,
    this.currentTier = SubscriptionTier.free,
    this.usageStatus,
  });

  PremiumState copyWith({
    bool? isPremium,
    String? currentPlan,
    DateTime? subscriptionEndDate,
    List<PremiumFeature>? features,
    List<SubscriptionPlan>? plans,
    bool? isLoading,
    String? errorMessage,
    SubscriptionTier? currentTier,
    UsageStatus? usageStatus,
  }) {
    return PremiumState(
      isPremium: isPremium ?? this.isPremium,
      currentPlan: currentPlan ?? this.currentPlan,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      features: features ?? this.features,
      plans: plans ?? this.plans,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      currentTier: currentTier ?? this.currentTier,
      usageStatus: usageStatus ?? this.usageStatus,
    );
  }

  @override
  List<Object?> get props => [
        isPremium,
        currentPlan,
        subscriptionEndDate,
        features,
        plans,
        isLoading,
        errorMessage,
        currentTier,
        usageStatus,
      ];
}

class PremiumFeature extends Equatable {
  final String id;
  final String title;
  final String description;
  final String iconName;
  final bool isAvailable;

  const PremiumFeature({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    this.isAvailable = true,
  });

  @override
  List<Object?> get props => [id, title, description, iconName, isAvailable];
}


