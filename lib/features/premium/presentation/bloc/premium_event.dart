import 'package:equatable/equatable.dart';

abstract class PremiumEvent extends Equatable {
  const PremiumEvent();

  @override
  List<Object?> get props => [];
}

class PremiumStatusLoaded extends PremiumEvent {}

class PremiumSubscriptionRequested extends PremiumEvent {
  final String planId;

  const PremiumSubscriptionRequested(this.planId);

  @override
  List<Object?> get props => [planId];
}

class PremiumSubscriptionCancelled extends PremiumEvent {}

class PremiumFeaturesLoaded extends PremiumEvent {}

class PremiumRestorePurchases extends PremiumEvent {}
