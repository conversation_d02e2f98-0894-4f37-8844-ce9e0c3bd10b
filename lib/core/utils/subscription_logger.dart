import '../utils/logger.dart';

class SubscriptionLogger {
  static const String _tag = 'Subscription';
  
  static void info(String message) {
    Logger.info(message, tag: _tag);
  }
  
  static void warning(String message) {
    Logger.warning(message, tag: _tag);
  }
  
  static void error(String message) {
    Logger.error(message, tag: _tag);
  }
}

class UsageLogger {
  static const String _tag = 'Usage';
  
  static void info(String message) {
    Logger.info(message, tag: _tag);
  }
  
  static void warning(String message) {
    Logger.warning(message, tag: _tag);
  }
  
  static void error(String message) {
    Logger.error(message, tag: _tag);
  }
}
