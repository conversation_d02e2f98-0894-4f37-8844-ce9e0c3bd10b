import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import '../constants/app_constants.dart';

class CloudinaryService {

  static const String _apiKey = AppConstants.cloudinaryApiKey;
  static const String _apiSecret = AppConstants.cloudinaryApiSecret;
  static const String _uploadUrl = AppConstants.cloudinaryUploadUrl;

  /// Upload an image file to Cloudinary
  /// Returns the secure URL of the uploaded image
  Future<String> uploadImage(File imageFile, {String? folder}) async {
    return uploadImageWithSignature(imageFile, folder: folder);
  }

  /// Generate signature for Cloudinary API
  String _generateSignature(Map<String, dynamic> params, String apiSecret) {
    // Sort parameters by key
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    // Create parameter string
    final paramString = sortedParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    // Add API secret
    final stringToSign = '$paramString$apiSecret';

    // Generate SHA1 hash
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  /// Upload image without upload preset (using signature)
  Future<String> uploadImageWithSignature(File imageFile, {String? folder}) async {
    try {
      // Generate timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      // Create parameters for signature
      final params = <String, dynamic>{
        'timestamp': timestamp,
      };
      
      if (folder != null) {
        params['folder'] = folder;
      }

      // Generate signature
      final signature = _generateSignature(params, _apiSecret);

      // Create multipart request
      final request = http.MultipartRequest('POST', Uri.parse(_uploadUrl));
      
      // Add file
      request.files.add(
        await http.MultipartFile.fromPath('file', imageFile.path),
      );
      
      // Add parameters
      request.fields.addAll({
        'api_key': _apiKey,
        'timestamp': timestamp.toString(),
        'signature': signature,
        if (folder != null) 'folder': folder,
      });

      // Send request
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        return jsonResponse['secure_url'] as String;
      } else {
        throw Exception('Failed to upload image: ${response.statusCode} - $responseBody');
      }
    } catch (e) {
      throw Exception('Error uploading image to Cloudinary: $e');
    }
  }
}
