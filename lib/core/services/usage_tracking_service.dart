import 'dart:async';
import '../../shared/models/usage_tracking.dart';
import '../../shared/models/subscription_tier.dart';
import '../utils/subscription_logger.dart';
import 'storage_service.dart';
import 'firebase_service.dart';
import 'subscription_service.dart';

class UsageTrackingService {
  final StorageService _storageService;
  final FirebaseService _firebaseService;
  final SubscriptionService _subscriptionService;
  
  final StreamController<UsageStatus> _usageController = StreamController<UsageStatus>.broadcast();
  
  UsageTrackingService({
    required StorageService storageService,
    required FirebaseService firebaseService,
    required SubscriptionService subscriptionService,
  }) : _storageService = storageService,
       _firebaseService = firebaseService,
       _subscriptionService = subscriptionService;

  Stream<UsageStatus> get usageStream => _usageController.stream;

  Future<void> initialize() async {
    // Listen to subscription tier changes
    _subscriptionService.tierStream.listen((_) {
      _updateUsageStatus();
    });
    
    // Initial usage status update
    await _updateUsageStatus();
  }

  Future<DailyUsage> _getDailyUsage() async {
    final user = _storageService.getUser();
    if (user == null) {
      throw Exception('User not found');
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    try {
      // Try to get from Firestore first
      final doc = await _firebaseService.firestore
          .collection('daily_usage')
          .doc('${user.id}_${today.toIso8601String().split('T')[0]}')
          .get();
      
      if (doc.exists) {
        final usage = DailyUsage.fromJson(doc.data()!);
        
        // Check if needs reset
        if (usage.needsReset()) {
          final resetUsage = usage.reset();
          await _saveDailyUsage(resetUsage);
          return resetUsage;
        }
        
        return usage;
      }
    } catch (e) {
      UsageLogger.warning('Failed to get usage from Firestore: $e');
    }

    // Create new daily usage
    final newUsage = DailyUsage(
      userId: user.id,
      date: today,
      messagesSent: 0,
      imagesSent: 0,
      lastReset: now,
    );
    
    await _saveDailyUsage(newUsage);
    return newUsage;
  }

  Future<void> _saveDailyUsage(DailyUsage usage) async {
    try {
      final today = DateTime(usage.date.year, usage.date.month, usage.date.day);
      await _firebaseService.firestore
          .collection('daily_usage')
          .doc('${usage.userId}_${today.toIso8601String().split('T')[0]}')
          .set(usage.toJson());
    } catch (e) {
      UsageLogger.warning('Failed to save usage to Firestore: $e');
      // Fallback to local storage
      await _storageService.setSetting('daily_usage', usage.toJson());
    }
  }

  Future<UsageStatus> getUsageStatus() async {
    try {
      final currentTier = await _subscriptionService.getCurrentTier();
      final limits = SubscriptionLimits.getLimits(currentTier);
      final usage = await _getDailyUsage();
      
      // Check companion count
      final user = _storageService.getUser();
      int companionCount = 0;
      if (user != null) {
        try {
          final companions = await _firebaseService.getUserCompanions(user.id);
          companionCount = companions.length;
        } catch (e) {
          UsageLogger.warning('Failed to get companion count: $e');
        }
      }

      // Calculate remaining limits
      final remainingMessages = limits.hasUnlimitedMessages 
          ? -1 
          : (limits.messagesPerDay - usage.messagesSent).clamp(0, limits.messagesPerDay);
      
      final remainingImages = limits.hasUnlimitedImages 
          ? -1 
          : (limits.imagesPerDay - usage.imagesSent).clamp(0, limits.imagesPerDay);

      // Check permissions
      final canSendMessage = limits.hasUnlimitedMessages || usage.messagesSent < limits.messagesPerDay;
      final canSendImage = limits.hasUnlimitedImages || usage.imagesSent < limits.imagesPerDay;
      final canCreateCompanion = limits.hasUnlimitedCompanions || companionCount < limits.companionLimit;

      // Generate limit message
      String? limitMessage;
      if (!canSendMessage) {
        limitMessage = 'Daily message limit reached (${limits.messagesPerDay}). Upgrade to send more!';
      } else if (!canSendImage && limits.imagesPerDay > 0) {
        limitMessage = 'Daily image limit reached (${limits.imagesPerDay}). Upgrade for more images!';
      } else if (!canCreateCompanion) {
        if (limits.companionLimit == 1) {
          limitMessage = 'Companion limit reached (${limits.companionLimit}). Upgrade for more companions!';
        } else {
          limitMessage = 'Companion limit reached (${limits.companionLimit}). Upgrade for unlimited companions!';
        }
      }

      return UsageStatus(
        canSendMessage: canSendMessage,
        canSendImage: canSendImage,
        canCreateCompanion: canCreateCompanion,
        remainingMessages: remainingMessages,
        remainingImages: remainingImages,
        limitMessage: limitMessage,
      );
    } catch (e) {
      UsageLogger.error('Failed to get usage status: $e');
      // Return permissive status on error
      return const UsageStatus(
        canSendMessage: true,
        canSendImage: true,
        canCreateCompanion: true,
        remainingMessages: -1,
        remainingImages: -1,
      );
    }
  }

  Future<bool> canSendMessage() async {
    final status = await getUsageStatus();
    return status.canSendMessage;
  }

  Future<bool> canSendImage() async {
    final status = await getUsageStatus();
    return status.canSendImage;
  }

  Future<bool> canCreateCompanion() async {
    final status = await getUsageStatus();
    return status.canCreateCompanion;
  }

  Future<void> incrementMessageCount() async {
    try {
      final usage = await _getDailyUsage();
      final updatedUsage = usage.copyWith(messagesSent: usage.messagesSent + 1);
      await _saveDailyUsage(updatedUsage);
      await _updateUsageStatus();
    } catch (e) {
      UsageLogger.error('Failed to increment message count: $e');
    }
  }

  Future<void> incrementImageCount() async {
    try {
      final usage = await _getDailyUsage();
      final updatedUsage = usage.copyWith(imagesSent: usage.imagesSent + 1);
      await _saveDailyUsage(updatedUsage);
      await _updateUsageStatus();
    } catch (e) {
      UsageLogger.error('Failed to increment image count: $e');
    }
  }

  Future<void> _updateUsageStatus() async {
    final status = await getUsageStatus();
    _usageController.add(status);
  }

  Future<bool> hasFeatureAccess(String featureId) async {
    final currentTier = await _subscriptionService.getCurrentTier();
    final limits = SubscriptionLimits.getLimits(currentTier);
    
    switch (featureId) {
      case 'memory':
        return limits.hasMemory;
      case 'profile_pic':
        return limits.hasProfilePic;
      case 'unlimited_messages':
        return limits.hasUnlimitedMessages;
      case 'unlimited_images':
        return limits.hasUnlimitedImages;
      case 'unlimited_companions':
        return limits.hasUnlimitedCompanions;
      default:
        return false;
    }
  }

  Future<bool> hasScenarioAccess(String scenarioId) async {
    final currentTier = await _subscriptionService.getCurrentTier();
    final limits = SubscriptionLimits.getLimits(currentTier);
    return limits.allowedScenarios.contains(scenarioId);
  }

  Future<bool> hasGameAccess(String gameId) async {
    final currentTier = await _subscriptionService.getCurrentTier();
    final limits = SubscriptionLimits.getLimits(currentTier);
    return limits.allowedGames.contains(gameId);
  }

  void dispose() {
    _usageController.close();
  }
}
