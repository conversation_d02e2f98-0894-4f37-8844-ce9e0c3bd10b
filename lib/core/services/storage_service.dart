import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/companion_model.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call initialize() first.');
    }
    return _prefs!;
  }

  // User data
  Future<void> saveUser(UserModel user) async {
    await prefs.setString(AppConstants.userDataKey, jsonEncode(user.toJson()));
  }

  UserModel? getUser() {
    final userJson = prefs.getString(AppConstants.userDataKey);
    if (userJson != null) {
      return UserModel.fromJson(jsonDecode(userJson) as Map<String, dynamic>);
    }
    return null;
  }

  Future<void> clearUser() async {
    await prefs.remove(AppConstants.userDataKey);
  }

  // Auth token
  Future<void> saveToken(String token) async {
    await prefs.setString(AppConstants.userTokenKey, token);
  }

  String? getToken() {
    return prefs.getString(AppConstants.userTokenKey);
  }

  Future<void> clearToken() async {
    await prefs.remove(AppConstants.userTokenKey);
  }

  // Selected companion
  Future<void> saveSelectedCompanion(CompanionModel companion) async {
    await prefs.setString(
      AppConstants.selectedCompanionKey,
      jsonEncode(companion.toJson()),
    );
  }

  CompanionModel? getSelectedCompanion() {
    final companionJson = prefs.getString(AppConstants.selectedCompanionKey);
    if (companionJson != null) {
      return CompanionModel.fromJson(
        jsonDecode(companionJson) as Map<String, dynamic>,
      );
    }
    return null;
  }

  Future<void> clearSelectedCompanion() async {
    await prefs.remove(AppConstants.selectedCompanionKey);
  }

  // Onboarding status
  Future<void> setOnboardingComplete(bool complete) async {
    await prefs.setBool(AppConstants.onboardingCompleteKey, complete);
  }

  bool isOnboardingComplete() {
    return prefs.getBool(AppConstants.onboardingCompleteKey) ?? false;
  }

  // Companions list (for offline storage)
  Future<void> saveCompanions(List<CompanionModel> companions) async {
    final companionsJson = companions.map((c) => c.toJson()).toList();
    await prefs.setString('companions', jsonEncode(companionsJson));
  }

  List<CompanionModel> getCompanions() {
    final companionsJson = prefs.getString('companions');
    if (companionsJson != null) {
      final List<dynamic> companionsList = jsonDecode(companionsJson);
      return companionsList
          .map((c) => CompanionModel.fromJson(c as Map<String, dynamic>))
          .toList();
    }
    return [];
  }

  // Active scenario/game persistence
  Future<void> saveActiveScenario(String scenarioId) async {
    await prefs.setString('active_scenario_id', scenarioId);
    // Clear active game when setting scenario
    await prefs.remove('active_game_id');
  }

  Future<void> saveActiveGame(String gameId) async {
    await prefs.setString('active_game_id', gameId);
    // Clear active scenario when setting game
    await prefs.remove('active_scenario_id');
  }

  String? getActiveScenario() {
    return prefs.getString('active_scenario_id');
  }

  String? getActiveGame() {
    return prefs.getString('active_game_id');
  }

  Future<void> clearActiveScenarioGame() async {
    await prefs.remove('active_scenario_id');
    await prefs.remove('active_game_id');
  }

  // App settings
  Future<void> setSetting(String key, dynamic value) async {
    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else {
      await prefs.setString(key, jsonEncode(value));
    }
  }

  T? getSetting<T>(String key) {
    final value = prefs.get(key);
    if (value is T) {
      return value;
    }
    return null;
  }

  // Generic data storage methods
  Future<void> saveData(String key, dynamic value) async {
    if (value is String) {
      await prefs.setString(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is List<String>) {
      await prefs.setStringList(key, value);
    } else {
      // For complex objects, store as JSON string
      await prefs.setString(key, jsonEncode(value));
    }
  }

  dynamic getData(String key) {
    final value = prefs.get(key);
    if (value is String) {
      // Try to decode as JSON, if it fails return as string
      try {
        return jsonDecode(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  Future<void> removeData(String key) async {
    await prefs.remove(key);
  }

  // Clear all data
  Future<void> clearAll() async {
    await prefs.clear();
  }
}
