import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/companion_model.dart';
import '../../shared/models/message_model.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Public getters
  FirebaseFirestore get firestore => _firestore;

  // Collections
  CollectionReference get _usersCollection => _firestore.collection('users');
  CollectionReference get _companionsCollection => _firestore.collection('companions');
  CollectionReference get _conversationsCollection => _firestore.collection('conversations');

  // Auth Methods
  User? get currentUser => _auth.currentUser;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      return await _auth.signInWithCredential(credential);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<UserCredential?> signInWithApple() async {
    try {
      // Check if Apple Sign In is available (iOS 13.0+ or macOS 10.15+)
      if (!await SignInWithApple.isAvailable()) {
        throw Exception('Apple Sign In is not available on this device');
      }

      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create Firebase credential
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      return await _auth.signInWithCredential(oauthCredential);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  Future<void> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // User Methods
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> createUser(UserModel user) async {
    try {
      await _usersCollection.doc(user.id).set(user.toJson());
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _usersCollection.doc(user.id).update(user.toJson());
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  // Companion Methods
  Future<List<CompanionModel>> getUserCompanions(String userId) async {
    print('🔵 FirebaseService: getUserCompanions started for userId: $userId');
    try {
      print('🔵 FirebaseService: Querying companions collection...');
      final query = await _companionsCollection
          .where('user_id', isEqualTo: userId)
          .orderBy('created_at', descending: false)
          .get();

      print('🔵 FirebaseService: Query completed, found ${query.docs.length} documents');

      final companions = query.docs
          .map((doc) => CompanionModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      print('🔵 FirebaseService: Parsed ${companions.length} companions');
      for (final companion in companions) {
        print('🔵 FirebaseService: - ${companion.name} (id: ${companion.id}, active: ${companion.isActive})');
      }

      print('🟢 FirebaseService: getUserCompanions completed successfully');
      return companions;
    } catch (e) {
      print('🔴 FirebaseService: Error in getUserCompanions: $e');
      throw _handleFirestoreError(e);
    }
  }

  Future<void> createCompanion(CompanionModel companion) async {
    try {
      await _companionsCollection.doc(companion.id).set(companion.toJson());
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> updateCompanion(CompanionModel companion) async {
    try {
      await _companionsCollection.doc(companion.id).update(companion.toJson());
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> setActiveCompanion(String userId, String companionId) async {
    try {
      // First, set all companions to inactive
      final batch = _firestore.batch();
      final companions = await getUserCompanions(userId);
      
      for (final companion in companions) {
        batch.update(
          _companionsCollection.doc(companion.id),
          {'is_active': companion.id == companionId},
        );
      }
      
      await batch.commit();
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  // Message Methods
  Stream<List<MessageModel>> getConversationMessages(String conversationId) {
    try {

      return _conversationsCollection
          .doc(conversationId)
          .collection('messages')
          .orderBy('datetime', descending: false)  // Changed from 'timestamp' to 'datetime'
          .snapshots()
          .map((snapshot) {

            return snapshot.docs
                .map((doc) {
                  final data = doc.data();
                  data['id'] = doc.id;  // Add document ID
                  data['conversation_id'] = conversationId;  // Add conversation ID

                  return MessageModel.fromJson(data);
                })
                .toList();
          });
    } catch (e) {

      throw _handleFirestoreError(e);
    }
  }

  Future<void> sendMessage(MessageModel message) async {
    try {
      await _conversationsCollection
          .doc(message.conversationId)
          .collection('messages')
          .doc(message.id)
          .set(message.toJson());

      // Update conversation's last updated time
      await _conversationsCollection.doc(message.conversationId).update({
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> createConversation(String conversationId, String userId, String companionId) async {
    try {
      await _conversationsCollection.doc(conversationId).set({
        'id': conversationId,
        'user_id': userId,
        'companion_id': companionId,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> updateMessageSaveStatus(String conversationId, String messageId, bool isSaved) async {
    try {
      print('Updating message save status: conversationId=$conversationId, messageId=$messageId, isSaved=$isSaved');

      final messageRef = _conversationsCollection
          .doc(conversationId)
          .collection('messages')
          .doc(messageId);

      // Check if message exists first
      final messageDoc = await messageRef.get();
      if (!messageDoc.exists) {
        throw Exception('Message not found: $messageId in conversation $conversationId');
      }

      // Update the is_saved field (use set with merge to create field if it doesn't exist)
      await messageRef.set({'is_saved': isSaved}, SetOptions(merge: true));
      print('Successfully updated message save status');
    } catch (e) {
      print('Error updating message save status: $e');
      throw _handleFirestoreError(e);
    }
  }

  Future<List<MessageModel>> getSavedMessages(String companionId) async {
    try {
      print('Getting saved messages for companion: $companionId');

      // Get the conversation for this companion
      final conversationQuery = await _conversationsCollection
          .where('companion_id', isEqualTo: companionId)
          .limit(1)
          .get();

      if (conversationQuery.docs.isEmpty) {
        print('No conversation found for companion: $companionId');
        return [];
      }

      final conversationId = conversationQuery.docs.first.id;
      print('Found conversation: $conversationId');

      // Get saved messages from this conversation
      // Query without orderBy to avoid composite index requirement
      final messagesQuery = await _conversationsCollection
          .doc(conversationId)
          .collection('messages')
          .where('is_saved', isEqualTo: true)
          .get();

      print('Found ${messagesQuery.docs.length} saved messages');

      final messages = messagesQuery.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        data['conversation_id'] = conversationId;

        // Handle field name conversion: datetime -> timestamp
        if (data['datetime'] != null && data['timestamp'] == null) {
          data['timestamp'] = data['datetime'];
        }

        return MessageModel.fromJson(data);
      }).toList();

      // Sort by timestamp in descending order (newest first)
      messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return messages;
    } catch (e) {
      print('Error getting saved messages: $e');
      throw _handleFirestoreError(e);
    }
  }

  /// Get all saved messages across all companions for a user
  Future<List<MessageModel>> getAllSavedMessages(String userId) async {
    try {
      print('Getting all saved messages for user: $userId');

      // Get all conversations for this user
      final conversationsQuery = await _conversationsCollection
          .where('user_id', isEqualTo: userId)
          .get();

      if (conversationsQuery.docs.isEmpty) {
        print('No conversations found for user: $userId');
        return [];
      }

      List<MessageModel> allSavedMessages = [];

      // Get saved messages from each conversation
      for (final conversationDoc in conversationsQuery.docs) {
        final conversationId = conversationDoc.id;
        final conversationData = conversationDoc.data() as Map<String, dynamic>?;
        final companionId = conversationData?['companion_id'] as String?;

        print('Checking conversation: $conversationId for companion: $companionId');

        // Get saved messages from this conversation
        final messagesQuery = await _conversationsCollection
            .doc(conversationId)
            .collection('messages')
            .where('is_saved', isEqualTo: true)
            .get();

        print('Found ${messagesQuery.docs.length} saved messages in conversation $conversationId');

        // Get companion name if companionId exists
        String? companionName;
        if (companionId != null) {
          try {
            final companionDoc = await _companionsCollection.doc(companionId).get();
            if (companionDoc.exists) {
              final companionData = companionDoc.data() as Map<String, dynamic>?;
              companionName = companionData?['name'] as String?;
            }
          } catch (e) {
            print('Error getting companion name for $companionId: $e');
          }
        }

        final messages = messagesQuery.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          data['conversation_id'] = conversationId;
          data['companion_id'] = companionId; // Add companion ID to message data
          data['companion_name'] = companionName; // Add companion name to message data

          // Handle field name conversion: datetime -> timestamp
          if (data['datetime'] != null && data['timestamp'] == null) {
            data['timestamp'] = data['datetime'];
          }

          return MessageModel.fromJson(data);
        }).toList();

        allSavedMessages.addAll(messages);
      }

      // Sort by timestamp in descending order (newest first)
      allSavedMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      print('Total saved messages found: ${allSavedMessages.length}');
      return allSavedMessages;
    } catch (e) {
      print('Error getting all saved messages: $e');
      throw _handleFirestoreError(e);
    }
  }

  // Error Handling
  String _handleAuthError(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-disabled':
          return 'This account has been disabled. Please contact support.';
        case 'user-not-found':
          return 'No account found with this email.';
        case 'wrong-password':
          return 'Incorrect password.';
        case 'email-already-in-use':
          return 'An account already exists with this email.';
        case 'weak-password':
          return 'Password is too weak.';
        case 'invalid-email':
          return 'Invalid email address.';
        case 'operation-not-allowed':
          return 'This sign-in method is not enabled.';
        case 'account-exists-with-different-credential':
          return 'An account already exists with a different sign-in method.';
        default:
          return 'Authentication failed. Please try again.';
      }
    }
    return 'An unexpected error occurred. Please try again.';
  }

  String _handleFirestoreError(dynamic error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return 'You don\'t have permission to perform this action.';
        case 'unavailable':
          return 'Service is currently unavailable. Please try again later.';
        case 'deadline-exceeded':
          return 'Request timed out. Please check your connection and try again.';
        case 'resource-exhausted':
          return 'Too many requests. Please try again later.';
        default:
          return 'A database error occurred. Please try again.';
      }
    }
    return 'An unexpected error occurred. Please try again.';
  }

  // Delete Account Methods
  Future<void> deleteUserAccount(String userId) async {
    try {
      final batch = _firestore.batch();

      // Delete all user's companions
      final companions = await getUserCompanions(userId);
      for (final companion in companions) {
        batch.delete(_companionsCollection.doc(companion.id));

        // Delete companion's conversation and messages
        if (companion.conversationId.isNotEmpty) {
          batch.delete(_conversationsCollection.doc(companion.conversationId));

          // Delete all messages in the conversation
          final messagesQuery = await _conversationsCollection
              .doc(companion.conversationId)
              .collection('messages')
              .get();

          for (final messageDoc in messagesQuery.docs) {
            batch.delete(messageDoc.reference);
          }
        }
      }

      // Delete user's saved messages
      final savedMessagesQuery = await _firestore
          .collection('saved_messages')
          .where('user_id', isEqualTo: userId)
          .get();

      for (final savedMessageDoc in savedMessagesQuery.docs) {
        batch.delete(savedMessageDoc.reference);
      }

      // Delete user document
      batch.delete(_usersCollection.doc(userId));

      // Commit all deletions
      await batch.commit();

      // Delete Firebase Auth user
      final user = _auth.currentUser;
      if (user != null && user.uid == userId) {
        await user.delete();
      }
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }

  Future<void> deleteCompanion(String companionId) async {
    try {
      final batch = _firestore.batch();

      // Get companion data first
      final companionDoc = await _companionsCollection.doc(companionId).get();
      if (!companionDoc.exists) {
        throw Exception('Companion not found');
      }

      final companionData = companionDoc.data() as Map<String, dynamic>;
      final conversationId = companionData['conversation_id'] as String?;

      // Delete companion
      batch.delete(_companionsCollection.doc(companionId));

      // Delete companion's conversation and messages if exists
      if (conversationId != null && conversationId.isNotEmpty) {
        batch.delete(_conversationsCollection.doc(conversationId));

        // Delete all messages in the conversation
        final messagesQuery = await _conversationsCollection
            .doc(conversationId)
            .collection('messages')
            .get();

        for (final messageDoc in messagesQuery.docs) {
          batch.delete(messageDoc.reference);
        }
      }

      await batch.commit();
    } catch (e) {
      throw _handleFirestoreError(e);
    }
  }
}
