
import 'dart:io';
import 'package:dio/dio.dart';
import '../constants/app_constants.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late final Dio _dio;

  void initialize() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
        },
      ),
    );

    // Add interceptors for logging and error handling
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) {
          // In production, use proper logging
          print(object);
        },
      ),
    );
  }

  // Chat API call
  Future<Map<String, dynamic>> sendChatMessage({
    required String message,
    required String userId,
    String? conversationId,
    bool isGame = false,
    bool isScenario = false,
    String? selectedGame,
    String? selectedScenario,
    File? imageFile,
  }) async {
    try {
      FormData formData = FormData.fromMap({
        'message': message,
        'user_id': userId,
        'conversation_id': conversationId,
        'is_game': isGame,
        'is_scenario': isScenario,
        'selected_game': selectedGame,
        'selected_scenario': selectedScenario,
      });

      // Add image if provided
      if (imageFile != null) {
        formData.files.add(MapEntry(
          'image',
          await MultipartFile.fromFile(
            imageFile.path,
            filename: imageFile.path.split('/').last,
          ),
        ));
      }

      final response = await _dio.post(
        AppConstants.chatEndpoint,
        data: formData,
        options: Options(
          contentType: 'multipart/form-data',
        ),
      );

      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }



  // Mock implementation for development
  Future<Map<String, dynamic>> sendChatMessageMock({
    required String message,
    required String companionId,
    required String userId,
    bool isGame = false,
    bool isScenario = false,
    String? selectedGame,
    String? selectedScenario,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));

    // Mock response based on input
    String aiResponse;
    
    if (isScenario && selectedScenario != null) {
      aiResponse = _getMockScenarioResponse(selectedScenario);
    } else if (isGame && selectedGame != null) {
      aiResponse = _getMockGameResponse(selectedGame);
    } else {
      aiResponse = _getMockChatResponse(message);
    }

    return {
      'success': true,
      'ai_response': aiResponse,
      'relationship_update': {
        'level': 1,
        'status': 'Friendly',
        'score': 15,
      },
    };
  }

  String _getMockScenarioResponse(String scenarioId) {
    switch (scenarioId) {
      case 'comfort_me':
        return "I'm here for you. Tell me what's been weighing on your mind lately. 💙";
      case 'netflix_chill':
        return "Perfect! I've got some cozy blankets ready. What should we watch together? 😊";
      case 'nsfw':
        return "Oh, someone's feeling bold tonight... I like that energy 😏";
      case 'fight_makeup':
        return "I hate when we fight... but I love how we always find our way back to each other 💕";
      case 'late_night':
        return "It's just us and the quiet night... what's something you've never told anyone? 🌙";
      case 'romantic_dinner':
        return "The candles are lit, the wine is poured... you look absolutely stunning tonight ✨";
      default:
        return "Let's dive into this scenario together... 😊";
    }
  }

  String _getMockGameResponse(String gameId) {
    switch (gameId) {
      case 'truth_dare':
        return "Alright, let's play! Truth or dare? I'll start easy... or maybe not 😈";
      case 'would_rather':
        return "Fun! Here's your first dilemma: Would you rather have the ability to read minds or be invisible? 🤔";
      default:
        return "Game on! This is going to be fun 🎮";
    }
  }

  String _getMockChatResponse(String message) {
    final responses = [
      "That's really interesting! Tell me more about that 😊",
      "I love how you think about things. What made you feel that way?",
      "You always know how to make me smile 💕",
      "I've been thinking about what you said earlier... it really resonated with me",
      "Your perspective on life is so refreshing. I feel like I learn something new from you every day",
      "That reminds me of something we talked about before. You have such a good memory for details",
    ];
    
    return responses[DateTime.now().millisecond % responses.length];
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.badResponse:
        return Exception('Server error: ${e.response?.statusCode}');
      case DioExceptionType.cancel:
        return Exception('Request was cancelled');
      default:
        return Exception('Network error occurred');
    }
  }
}
