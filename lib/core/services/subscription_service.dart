import 'dart:async';
import 'package:purchases_flutter/purchases_flutter.dart';
import '../../shared/models/subscription_tier.dart';
import '../utils/subscription_logger.dart';
import 'storage_service.dart';

class SubscriptionService {
  static const String _revenueCatApiKey = 'appl_cBXlDMwPLcbWHwdjdUCVEWqBzwR'; // Replace with actual key
  
  // RevenueCat product identifiers
  static const String plusMonthlyId = 'nova_soul_plus_monthly';
  static const String proMonthlyId = 'nova_soul_pro_monthly';
  static const String eliteMonthlyId = 'nova_soul_elite_monthly';
  
  final StorageService _storageService;
  final StreamController<SubscriptionTier> _tierController = StreamController<SubscriptionTier>.broadcast();
  
  SubscriptionService({required StorageService storageService}) : _storageService = storageService;

  Stream<SubscriptionTier> get tierStream => _tierController.stream;

  Future<void> initialize() async {
    try {
      // Configure RevenueCat
      await Purchases.setLogLevel(LogLevel.debug);
      
      PurchasesConfiguration configuration = PurchasesConfiguration(_revenueCatApiKey);
      await Purchases.configure(configuration);
      
      // Set up listener for subscription changes
      Purchases.addCustomerInfoUpdateListener(_onCustomerInfoUpdate);
      
      // Load initial subscription status
      await _loadSubscriptionStatus();
    } catch (e) {
      SubscriptionLogger.error('Failed to initialize subscription service: $e');
      // Fallback to stored subscription status
      await _loadSubscriptionStatus();
    }
  }

  Future<void> _loadSubscriptionStatus() async {
    try {
      // Try to get from RevenueCat first
      final customerInfo = await Purchases.getCustomerInfo();
      final tier = _getTierFromCustomerInfo(customerInfo);
      
      // Save to local storage
      await _storageService.setSetting('subscription_tier', tier.name);
      _tierController.add(tier);
    } catch (e) {
      // Fallback to local storage
      final storedTier = _storageService.getSetting<String>('subscription_tier');
      final tier = SubscriptionTier.fromString(storedTier);
      _tierController.add(tier);
    }
  }

  SubscriptionTier _getTierFromCustomerInfo(CustomerInfo customerInfo) {
    final activeSubscriptions = customerInfo.activeSubscriptions;
    
    if (activeSubscriptions.contains(eliteMonthlyId)) {
      return SubscriptionTier.elite;
    } else if (activeSubscriptions.contains(proMonthlyId)) {
      return SubscriptionTier.pro;
    } else if (activeSubscriptions.contains(plusMonthlyId)) {
      return SubscriptionTier.plus;
    }
    
    return SubscriptionTier.free;
  }

  void _onCustomerInfoUpdate(CustomerInfo customerInfo) {
    final tier = _getTierFromCustomerInfo(customerInfo);
    _storageService.setSetting('subscription_tier', tier.name);
    _tierController.add(tier);
  }

  Future<SubscriptionTier> getCurrentTier() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      return _getTierFromCustomerInfo(customerInfo);
    } catch (e) {
      // Fallback to stored tier
      final storedTier = _storageService.getSetting<String>('subscription_tier');
      return SubscriptionTier.fromString(storedTier);
    }
  }

  Future<List<SubscriptionPlan>> getAvailablePlans() async {
    try {
      final offerings = await Purchases.getOfferings();
      final currentOffering = offerings.current;
      
      if (currentOffering == null) {
        return _getDefaultPlans();
      }

      final plans = <SubscriptionPlan>[];
      
      // Plus Plan
      final plusPackage = currentOffering.getPackage(plusMonthlyId);
      if (plusPackage != null) {
        plans.add(SubscriptionPlan(
          id: plusMonthlyId,
          tier: SubscriptionTier.plus,
          name: 'Plus',
          price: plusPackage.storeProduct.priceString,
          period: 'per month',
          features: _getPlusFeatures(),
        ));
      }

      // Pro Plan (Recommended)
      final proPackage = currentOffering.getPackage(proMonthlyId);
      if (proPackage != null) {
        plans.add(SubscriptionPlan(
          id: proMonthlyId,
          tier: SubscriptionTier.pro,
          name: 'Pro',
          price: proPackage.storeProduct.priceString,
          period: 'per month',
          isRecommended: true,
          features: _getProFeatures(),
        ));
      }

      // Elite Plan
      final elitePackage = currentOffering.getPackage(eliteMonthlyId);
      if (elitePackage != null) {
        plans.add(SubscriptionPlan(
          id: eliteMonthlyId,
          tier: SubscriptionTier.elite,
          name: 'Elite',
          price: elitePackage.storeProduct.priceString,
          period: 'per month',
          features: _getEliteFeatures(),
        ));
      }

      return plans.isNotEmpty ? plans : _getDefaultPlans();
    } catch (e) {
      return _getDefaultPlans();
    }
  }

  List<SubscriptionPlan> _getDefaultPlans() {
    return [
      SubscriptionPlan(
        id: plusMonthlyId,
        tier: SubscriptionTier.plus,
        name: 'Plus',
        price: '\$4.99',
        period: 'per month',
        features: _getPlusFeatures(),
      ),
      SubscriptionPlan(
        id: proMonthlyId,
        tier: SubscriptionTier.pro,
        name: 'Pro',
        price: '\$9.99',
        period: 'per month',
        isRecommended: true,
        features: _getProFeatures(),
      ),
      SubscriptionPlan(
        id: eliteMonthlyId,
        tier: SubscriptionTier.elite,
        name: 'Elite',
        price: '\$14.99',
        period: 'per month',
        features: _getEliteFeatures(),
      ),
    ];
  }

  List<String> _getPlusFeatures() {
    return [
      'AI remembers and recalls memories',
      'Access to 2 exclusive scenarios',
      '100 messages/day',
      '2 companions',
      'Comfort Me & Mental Health scenarios',
    ];
  }

  List<String> _getProFeatures() {
    return [
      'AI remembers and recalls memories',
      'Set custom profile pics',
      'Send 1 image/day',
      'Unlock ALL Scenarios & Games',
      '300 messages/day',
      '4 companions',
    ];
  }

  List<String> _getEliteFeatures() {
    return [
      'AI remembers and recalls memories',
      'Set custom profile pics',
      'Unlimited image sending',
      'Unlock ALL Scenarios & Games',
      'Unlimited messages/day',
    ];
  }

  Future<bool> purchaseSubscription(String productId) async {
    try {
      final offerings = await Purchases.getOfferings();
      final currentOffering = offerings.current;
      
      if (currentOffering == null) {
        throw Exception('No offerings available');
      }

      final package = currentOffering.getPackage(productId);
      if (package == null) {
        throw Exception('Product not found');
      }

      final purchaseResult = await Purchases.purchasePackage(package);
      final tier = _getTierFromCustomerInfo(purchaseResult.customerInfo);
      
      await _storageService.setSetting('subscription_tier', tier.name);
      _tierController.add(tier);
      
      return true;
    } catch (e) {
      SubscriptionLogger.error('Purchase failed: $e');
      return false;
    }
  }

  Future<bool> restorePurchases() async {
    try {
      final customerInfo = await Purchases.restorePurchases();
      final tier = _getTierFromCustomerInfo(customerInfo);
      
      await _storageService.setSetting('subscription_tier', tier.name);
      _tierController.add(tier);
      
      return true;
    } catch (e) {
      SubscriptionLogger.error('Restore failed: $e');
      return false;
    }
  }

  void dispose() {
    _tierController.close();
  }
}
