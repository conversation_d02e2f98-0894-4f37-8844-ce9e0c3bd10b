class AppConstants {
  // App Info
  static const String appName = 'Nova Soul';
  static const String appVersion = '1.0.0';
  
  // API
  static const String baseUrl = 'https://nova-soul-backend-production.up.railway.app/';
  static const String chatEndpoint = '/send_message';

  // Cloudinary Configuration
  static const String cloudinaryCloudName = 'dgvyd70ml';
  static const String cloudinaryApiKey = '554869465759851';
  static const String cloudinaryApiSecret = 'PMRbLVwLO_WLsubsOI1nFdRERJE';
  static const String cloudinaryUploadUrl = 'https://api.cloudinary.com/v1_1/dgvyd70ml/image/upload';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String selectedCompanionKey = 'selected_companion';
  static const String onboardingCompleteKey = 'onboarding_complete';
  
  // Companion Types
  static const String companionTypeRomantic = 'romantic';
  static const String companionTypeFriendship = 'friendship';
  
  // Personality Types
  static const String personalityNormal = 'normal';
  static const String personalityWildcard = 'wildcard';
  
  // Gender Options
  static const String genderMale = 'male';
  static const String genderFemale = 'female';
  static const String genderNonBinary = 'non_binary';
  static const String genderPreferNotToSay = 'prefer_not_to_say';
  
  // Message Types
  static const String messageSenderHuman = 'human';
  static const String messageSenderAI = 'ai';
  
  // Scenarios
  static const List<Map<String, dynamic>> scenarios = [
    {
      'id': 'comfort_me',
      'title': 'Comfort Me',
      'description': 'Gentle, parental comfort and safety',
      'color': 0xFF4FC3F7,
      'icon': '🛡️',
    },
    {
      'id': 'mental_health_checkin',
      'title': 'Mental Health Check-In',
      'description': 'Supportive mental wellness conversation',
      'color': 0xFF81C784,
      'icon': '💚',
    },
    {
      'id': 'i_missed_you',
      'title': 'I Missed You',
      'description': 'A warm, emotional reunion',
      'color': 0xFFFFB74D,
      'icon': '💕',
    },
    {
      'id': 'netflix_chill',
      'title': 'Netflix & Chill',
      'description': 'Casual, flirty couch vibes',
      'color': 0xFFAB47BC,
      'icon': '📺',
    },
    {
      'id': 'nsfw',
      'title': 'Spicy',
      'description': 'Teasing, suggestive, and daring',
      'color': 0xFFE57373,
      'icon': '🔥',
    },
    {
      'id': 'fight_makeup',
      'title': 'Fight & Make Up',
      'description': 'Emotional tension and resolution',
      'color': 0xFFFF8A65,
      'icon': '💔',
    },
    {
      'id': 'late_night',
      'title': 'Late Night Confessions',
      'description': 'Deep, vulnerable, and honest talks',
      'color': 0xFF7986CB,
      'icon': '🌙',
    },
    {
      'id': 'romantic_dinner',
      'title': 'Romantic Dinner',
      'description': 'Intimate dining experience',
      'color': 0xFFFF8A80,
      'icon': '🍽️',
    },
  ];
  
  // Games
  static const List<Map<String, dynamic>> games = [
    {
      'id': 'truth_dare',
      'title': 'Truth or Dare',
      'description': 'Spicy questions & fun challenges',
      'color': 0xFFFFB74D,
      'icon': '❓',
    },
    {
      'id': 'would_rather',
      'title': 'Would You Rather',
      'description': 'Funny, flirty, or deep dilemmas',
      'color': 0xFF81C784,
      'icon': '🤔',
    },
  ];
  
  // Relationship Levels
  static const List<String> relationshipLevels = [
    'Stranger',
    'Acquaintance',
    'Friend',
    'Close Friend',
    'Best Friend',
    'Romantic Interest',
    'Partner',
    'Soulmate',
  ];
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}
