import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Dark Theme Colors
  static const Color darkPrimaryColor = Color(0xFF121212); // True dark
  static const Color darkSecondaryColor = Color(0xFF1E1E1E); // Dark gray
  static const Color darkAccentColor = Color(0xFF4FC3F7); // Sky blue accent
  static const Color darkSurfaceColor = Color(0xFF1E1E1E); // Dark surface
  static const Color darkBackgroundColor = Color(0xFF121212); // True dark background
  static const Color darkUserMessageColor = Color(0xFF4FC3F7); // Sky blue
  static const Color darkAiMessageColor = Color(0xFF2A2A2A); // Dark gray
  static const Color darkPrimaryTextColor = Color(0xFFFFFFFF); // White text
  static const Color darkSecondaryTextColor = Color(0xFFB0B0B0); // Light gray text
  static const Color darkHintTextColor = Color(0xFF666666); // Medium gray text

  // Light Theme Colors
  static const Color lightPrimaryColor = Color(0xFFFFFFFF); // White primary
  static const Color lightSecondaryColor = Color(0xFFF5F5F5); // Light gray
  static const Color lightAccentColor = Color(0xFF4FC3F7); // Sky blue accent
  static const Color lightSurfaceColor = Color(0xFFFFFFFF); // White surface
  static const Color lightBackgroundColor = Color(0xFFF8F9FA); // Very light gray background
  static const Color lightUserMessageColor = Color(0xFF4FC3F7); // Sky blue (same as dark)
  static const Color lightAiMessageColor = Color(0xFFF1F3F4); // Light gray
  static const Color lightPrimaryTextColor = Color(0xFF1A1A1A); // Dark text
  static const Color lightSecondaryTextColor = Color(0xFF5F6368); // Medium gray text
  static const Color lightHintTextColor = Color(0xFF9AA0A6); // Light gray text

  // Shared Accent Colors (work well in both themes)
  static const Color friendlyColor = Color(0xFFFFD54F);
  static const Color romanticColor = Color(0xFFFF8A80);
  static const Color successColor = Color(0xFF81C784);
  static const Color warningColor = Color(0xFFFFB74D);
  static const Color errorColor = Color(0xFFE57373);

  // Legacy colors for backward compatibility (dark theme)
  static const Color primaryColor = darkPrimaryColor;
  static const Color secondaryColor = darkSecondaryColor;
  static const Color accentColor = darkAccentColor;
  static const Color surfaceColor = darkSurfaceColor;
  static const Color backgroundColor = darkBackgroundColor;
  static const Color userMessageColor = darkUserMessageColor;
  static const Color aiMessageColor = darkAiMessageColor;
  static const Color primaryTextColor = darkPrimaryTextColor;
  static const Color secondaryTextColor = darkSecondaryTextColor;
  static const Color hintTextColor = darkHintTextColor;
  
  // Dark Theme Gradients
  static const LinearGradient darkPrimaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkPrimaryColor, darkSecondaryColor],
  );

  static const LinearGradient darkMessageGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkUserMessageColor, Color(0xFF29B6F6)],
  );

  // Light Theme Gradients
  static const LinearGradient lightPrimaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [lightPrimaryColor, lightSecondaryColor],
  );

  static const LinearGradient lightMessageGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [lightUserMessageColor, Color(0xFF29B6F6)],
  );

  // Legacy gradients for backward compatibility (dark theme)
  static const LinearGradient primaryGradient = darkPrimaryGradient;
  static const LinearGradient messageGradient = darkMessageGradient;
  
  // Theme Data
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: darkPrimaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      colorScheme: const ColorScheme.dark(
        primary: darkUserMessageColor, // Sky blue as primary
        secondary: darkSecondaryColor,
        surface: darkSurfaceColor,
        onPrimary: darkBackgroundColor, // Dark text on sky blue
        onSecondary: darkPrimaryTextColor,
        onSurface: darkPrimaryTextColor,
      ),
      textTheme: GoogleFonts.interTextTheme(
        const TextTheme(
          displayLarge: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
          displayMedium: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
          displaySmall: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),
          headlineLarge: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
          headlineMedium: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
          headlineSmall: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          titleLarge: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          titleMedium: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          titleSmall: TextStyle(
            color: darkSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          bodyLarge: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 16,
            fontWeight: FontWeight.normal,
          ),
          bodyMedium: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
          bodySmall: TextStyle(
            color: darkSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          labelLarge: TextStyle(
            color: darkPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          labelMedium: TextStyle(
            color: darkSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          labelSmall: TextStyle(
            color: darkHintTextColor,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: darkBackgroundColor,
        foregroundColor: darkPrimaryTextColor,
        elevation: 0,
        centerTitle: true,
        surfaceTintColor: Colors.transparent,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: darkBackgroundColor,
        selectedItemColor: darkUserMessageColor,
        unselectedItemColor: darkSecondaryTextColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: darkUserMessageColor,
          foregroundColor: darkPrimaryTextColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        hintStyle: const TextStyle(color: darkHintTextColor),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      cardTheme: CardThemeData(
        color: darkSurfaceColor,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: lightPrimaryColor,
      scaffoldBackgroundColor: lightBackgroundColor,
      colorScheme: const ColorScheme.light(
        primary: lightUserMessageColor, // Sky blue as primary
        secondary: lightSecondaryColor,
        surface: lightSurfaceColor,
        onPrimary: lightBackgroundColor, // White text on sky blue
        onSecondary: lightPrimaryTextColor,
        onSurface: lightPrimaryTextColor,
      ),
      textTheme: GoogleFonts.interTextTheme(
        const TextTheme(
          displayLarge: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
          displayMedium: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
          displaySmall: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),
          headlineLarge: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
          headlineMedium: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
          headlineSmall: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          titleLarge: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          titleMedium: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          titleSmall: TextStyle(
            color: lightSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          bodyLarge: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 16,
            fontWeight: FontWeight.normal,
          ),
          bodyMedium: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.normal,
          ),
          bodySmall: TextStyle(
            color: lightSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          labelLarge: TextStyle(
            color: lightPrimaryTextColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          labelMedium: TextStyle(
            color: lightSecondaryTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          labelSmall: TextStyle(
            color: lightHintTextColor,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: lightBackgroundColor,
        foregroundColor: lightPrimaryTextColor,
        elevation: 0,
        centerTitle: true,
        surfaceTintColor: Colors.transparent,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: lightBackgroundColor,
        selectedItemColor: lightUserMessageColor,
        unselectedItemColor: lightSecondaryTextColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: lightUserMessageColor,
          foregroundColor: lightBackgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        hintStyle: const TextStyle(color: lightHintTextColor),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      cardTheme: CardThemeData(
        color: lightSurfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
