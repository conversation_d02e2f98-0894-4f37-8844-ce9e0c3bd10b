import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/storage_service.dart';
import '../app_theme.dart';
import 'theme_event.dart';
import 'theme_state.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final StorageService _storageService;
  static const String _themeKey = 'app_theme_mode';

  ThemeBloc({
    required StorageService storageService,
  })  : _storageService = storageService,
        super(ThemeState(
          isDarkMode: true, // Default to dark mode
          themeData: AppTheme.darkTheme,
        )) {
    on<ThemeInitialized>(_onThemeInitialized);
    on<ThemeToggled>(_onThemeToggled);
    on<ThemeChanged>(_onThemeChanged);
  }

  Future<void> _onThemeInitialized(
    ThemeInitialized event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      // Load saved theme preference, default to dark mode if not found
      final isDarkMode = _storageService.getSetting<bool>(_themeKey) ?? true;
      
      emit(ThemeState(
        isDarkMode: isDarkMode,
        themeData: isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      ));
    } catch (e) {
      // If there's an error loading the theme, default to dark mode
      emit(ThemeState(
        isDarkMode: true,
        themeData: AppTheme.darkTheme,
      ));
    }
  }

  Future<void> _onThemeToggled(
    ThemeToggled event,
    Emitter<ThemeState> emit,
  ) async {
    final newIsDarkMode = !state.isDarkMode;
    
    try {
      // Save the new theme preference
      await _storageService.setSetting(_themeKey, newIsDarkMode);
      
      emit(ThemeState(
        isDarkMode: newIsDarkMode,
        themeData: newIsDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      ));
    } catch (e) {
      // If saving fails, still update the UI but don't persist
      emit(ThemeState(
        isDarkMode: newIsDarkMode,
        themeData: newIsDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      ));
    }
  }

  Future<void> _onThemeChanged(
    ThemeChanged event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      // Save the new theme preference
      await _storageService.setSetting(_themeKey, event.isDarkMode);
      
      emit(ThemeState(
        isDarkMode: event.isDarkMode,
        themeData: event.isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      ));
    } catch (e) {
      // If saving fails, still update the UI but don't persist
      emit(ThemeState(
        isDarkMode: event.isDarkMode,
        themeData: event.isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      ));
    }
  }
}
