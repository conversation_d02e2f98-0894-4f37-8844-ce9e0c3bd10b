// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCL4l0zcC1A30wKkcsqjtZef9vHYpyYYMA',
    appId: '1:960655030374:web:46c12968a283281be295a7',
    messagingSenderId: '960655030374',
    projectId: 'nova-soul',
    authDomain: 'nova-soul.firebaseapp.com',
    storageBucket: 'nova-soul.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB2KST14B7FxH2RwfIRwZMCxehzeZwBrcs',
    appId: '1:960655030374:android:599958933a0aefb8e295a7',
    messagingSenderId: '960655030374',
    projectId: 'nova-soul',
    storageBucket: 'nova-soul.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBdFpLo9PPwPu90v2HCq2EP63gIQmyYi_M',
    appId: '1:960655030374:ios:f969c191a4e17e2de295a7',
    messagingSenderId: '960655030374',
    projectId: 'nova-soul',
    storageBucket: 'nova-soul.firebasestorage.app',
    iosClientId: '960655030374-02fgkj1ei4v8vsmrh0oldpu37pmd9n51.apps.googleusercontent.com',
    iosBundleId: 'com.novasoul.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBdFpLo9PPwPu90v2HCq2EP63gIQmyYi_M',
    appId: '1:960655030374:ios:cb63b27125571d25e295a7',
    messagingSenderId: '960655030374',
    projectId: 'nova-soul',
    storageBucket: 'nova-soul.firebasestorage.app',
    iosBundleId: 'com.example.novaSoul',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCL4l0zcC1A30wKkcsqjtZef9vHYpyYYMA',
    appId: '1:960655030374:web:1720c98f8cf0db9be295a7',
    messagingSenderId: '960655030374',
    projectId: 'nova-soul',
    authDomain: 'nova-soul.firebaseapp.com',
    storageBucket: 'nova-soul.firebasestorage.app',
  );
}