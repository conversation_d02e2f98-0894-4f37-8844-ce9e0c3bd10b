import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/bloc/theme_bloc.dart';
import 'core/theme/bloc/theme_event.dart';
import 'core/theme/bloc/theme_state.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/firebase_service.dart';
import 'core/services/subscription_service.dart';
import 'core/services/usage_tracking_service.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/auth/presentation/bloc/auth_event.dart';
import 'features/auth/presentation/bloc/auth_state.dart';
import 'features/profile/presentation/bloc/profile_bloc.dart';
import 'features/profile/presentation/bloc/profile_state.dart';
import 'features/companion/presentation/bloc/companion_bloc.dart';
import 'features/companion/presentation/bloc/companion_event.dart';
import 'features/companion/presentation/bloc/companion_state.dart';
import 'features/chat/presentation/bloc/chat_bloc.dart';
import 'features/chat/presentation/bloc/chat_event.dart';
import 'features/chat/data/chat_service.dart';
import 'features/saved/presentation/bloc/saved_bloc.dart';
import 'features/premium/presentation/bloc/premium_bloc.dart';
import 'features/premium/presentation/bloc/premium_event.dart';
import 'features/auth/presentation/pages/sign_in_page.dart';
import 'features/profile/presentation/pages/profile_setup_page.dart';
import 'features/companion/presentation/pages/companion_setup_page.dart';
import 'features/chat/presentation/pages/home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize services
  final storageService = StorageService();
  await storageService.initialize();

  final apiService = ApiService();
  apiService.initialize();

  final firebaseService = FirebaseService();

  final subscriptionService = SubscriptionService(storageService: storageService);
  await subscriptionService.initialize();

  final usageTrackingService = UsageTrackingService(
    storageService: storageService,
    firebaseService: firebaseService,
    subscriptionService: subscriptionService,
  );
  await usageTrackingService.initialize();

  final chatService = ChatService(
    firebaseService: firebaseService,
    apiService: apiService,
  );

  runApp(NovaSoulApp(
    storageService: storageService,
    apiService: apiService,
    firebaseService: firebaseService,
    chatService: chatService,
    subscriptionService: subscriptionService,
    usageTrackingService: usageTrackingService,
  ));
}

class NovaSoulApp extends StatelessWidget {
  final StorageService storageService;
  final ApiService apiService;
  final FirebaseService firebaseService;
  final ChatService chatService;
  final SubscriptionService subscriptionService;
  final UsageTrackingService usageTrackingService;

  const NovaSoulApp({
    super.key,
    required this.storageService,
    required this.apiService,
    required this.firebaseService,
    required this.chatService,
    required this.subscriptionService,
    required this.usageTrackingService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc(
            storageService: storageService,
          )..add(const ThemeInitialized()),
        ),
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(
            storageService: storageService,
            firebaseService: firebaseService,
          )..add(AuthCheckRequested()),
        ),
        BlocProvider<ProfileBloc>(
          create: (context) => ProfileBloc(
            storageService: storageService,
            firebaseService: firebaseService,
          ),
        ),
        BlocProvider<CompanionBloc>(
          create: (context) => CompanionBloc(
            storageService: storageService,
            firebaseService: firebaseService,
            usageTrackingService: usageTrackingService,
          ),
        ),
        BlocProvider<ChatBloc>(
          create: (context) => ChatBloc(
            apiService: apiService,
            storageService: storageService,
            firebaseService: firebaseService,
            usageTrackingService: usageTrackingService,
          ),
        ),
        BlocProvider<SavedBloc>(
          create: (context) => SavedBloc(
            chatService: chatService,
            storageService: storageService,
          ),
        ),
        BlocProvider<PremiumBloc>(
          create: (context) => PremiumBloc(
            storageService: storageService,
            subscriptionService: subscriptionService,
            usageTrackingService: usageTrackingService,
          )..add(PremiumStatusLoaded()),
        ),
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp(
            title: 'Nova Soul',
            theme: themeState.themeData,
            home: const AppNavigator(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

class AppNavigator extends StatelessWidget {
  const AppNavigator({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        if (authState is AuthLoading) {
          return Scaffold(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          );
        }

        if (authState is AuthUnauthenticated) {
          return const SignInPage();
        }

        if (authState is AuthAuthenticated) {
          final user = authState.user;
          print('🔵 AppNavigator: AuthAuthenticated - user.id=${user.id}, username="${user.username}", gender="${user.gender}", onboardingComplete=${user.isOnboardingComplete}');

          // Check if user needs to complete profile setup
          if (user.username.isEmpty || user.gender.isEmpty) {
            print('🔵 AppNavigator: User needs profile setup - showing ProfileSetupPage');
            return BlocListener<ProfileBloc, ProfileState>(
              listener: (context, profileState) {
                print('🔵 AppNavigator: ProfileBloc state changed - isSubmitted=${profileState.isSubmitted}, isLoading=${profileState.isLoading}, errorMessage=${profileState.errorMessage}');
                if (profileState.isSubmitted) {
                  print('🔵 AppNavigator: Profile submitted - refreshing auth state');
                  // Refresh auth state to get updated user
                  context.read<AuthBloc>().add(AuthCheckRequested());
                }
              },
              child: const ProfileSetupPage(),
            );
          }
          print('🔵 AppNavigator: User profile complete - proceeding to main app');

          // Check if user needs to create a companion
          return BlocBuilder<CompanionBloc, CompanionState>(
            builder: (context, companionState) {
              print('🔵 AppNavigator: CompanionBloc state - isLoading=${companionState.isLoading}, companions.length=${companionState.companions.length}, errorMessage=${companionState.errorMessage}, selectedCompanion=${companionState.selectedCompanion?.name}');

              // Main app navigation logic
              // Load companions only when we reach this point (after profile is complete)
              // Check if we need to load companions for the first time
              if (!companionState.isLoading &&
                  companionState.companions.isEmpty &&
                  companionState.errorMessage == null &&
                  !companionState.hasLoadedOnce) {
                print('🔵 AppNavigator: No companions found, triggering CompanionsLoaded event');
                // Trigger companions loading for the first time
                context.read<CompanionBloc>().add(CompanionsLoaded());
                print('🔵 AppNavigator: Showing loading spinner while companions load');
                return Scaffold(
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  body: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                );
              }

              // Show loading while companions are being loaded
              if (companionState.isLoading) {
                print('🔵 AppNavigator: CompanionBloc is loading - showing loading screen');
                return const Scaffold(
                  backgroundColor: AppTheme.backgroundColor,
                  body: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.userMessageColor,
                      ),
                    ),
                  ),
                );
              }

              // Handle error case
              if (companionState.errorMessage != null) {
                print('🔴 AppNavigator: CompanionBloc error - ${companionState.errorMessage}');
                return Scaffold(
                  backgroundColor: AppTheme.backgroundColor,
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppTheme.errorColor,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading companions',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          companionState.errorMessage!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            context.read<CompanionBloc>().add(CompanionsLoaded());
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Show companion setup if no companions exist
              if (companionState.companions.isEmpty) {
                print('🔵 AppNavigator: No companions exist - showing CompanionSetupPage');
                return BlocListener<CompanionBloc, CompanionState>(
                  listener: (context, state) {
                    print('🔵 AppNavigator: CompanionBloc listener - isSubmitted=${state.isSubmitted}, companions.length=${state.companions.length}');
                    if (state.isSubmitted && state.companions.isNotEmpty) {
                      print('🔵 AppNavigator: Companion created - loading into chat');
                      // Load the companion into chat
                      final companion = state.companions.first;
                      context.read<ChatBloc>().add(
                            ChatCompanionChanged(companion.id),
                          );
                    }
                  },
                  child: const CompanionSetupPage(isOnboarding: true),
                );
              }

              // Initialize chat with selected companion
              if (companionState.selectedCompanion != null) {
                print('🔵 AppNavigator: Selected companion found - ${companionState.selectedCompanion!.name}, initializing chat');
                context.read<ChatBloc>().add(
                      ChatCompanionChanged(companionState.selectedCompanion!.id),
                    );
              } else {
                print('🔴 AppNavigator: No selected companion found but companions exist');
              }

              print('🟢 AppNavigator: Navigating to HomePage');
              return const HomePage();
            },
          );
        }

        // Default fallback
        return const SignInPage();
      },
    );
  }
}
