{"cells": [{"cell_type": "code", "execution_count": null, "id": "0940364d", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import TypedDict, List\n", "from langchain_core.messages import BaseMessage, AIMessage, SystemMessage,HumanMessage\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langgraph.graph import StateGraph, END, START\n", "from pydantic import BaseModel, Field\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c02febcc", "metadata": {}, "outputs": [], "source": ["class ScoringResult(BaseModel):\n", "    score_change:int = Field(\n", "        ...,\n", "        ge = -2,\n", "        le = 2,\n", "        description = 'Relationship change score from -2 to +2'\n", "    )\n", "    reasoning:str = Field(\n", "        ...,\n", "        description = 'brief explanation for the score change'\n", "    )\n", "\n", "class RelationshipDescription(BaseModel):\n", "    description: str = Field(\n", "        ...,\n", "        description=\"Detailed description of the current relationship status based on the score\"\n", "    )\n"]}, {"cell_type": "code", "execution_count": 25, "id": "046e83fe", "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    ai_message:str\n", "    current_score:int\n", "    final_score:int\n", "    relationship_description:str"]}, {"cell_type": "code", "execution_count": 26, "id": "ea2612ab", "metadata": {}, "outputs": [], "source": ["llm = ChatGoogleGenerativeAI(model=\"gemini-2.0-flash\", temperature=0.3)"]}, {"cell_type": "code", "execution_count": 27, "id": "46378b7d", "metadata": {}, "outputs": [], "source": ["def scoring_node(state:AgentState)->AgentState:\n", "    ai_response = state['ai_message']\n", "    current_score = state['current_score']\n", "    system_msg= SystemMessage(content=\"\"\"\n", "You are a relationship dynamics expert who evaluates AI responses to determine their impact on human-AI connection.\n", "\n", "SCORING CRITERIA:\n", "Score from -2 to +2 based on how the AI response affects the relationship:\n", "\n", "+2 (Significantly Deepens Connection):\n", "- Highly supportive, empathetic responses\n", "- Playful, flirty, or romantic interactions that feel genuine\n", "- Vulnerable, intimate sharing that builds trust\n", "- Celebrates user achievements meaningfully\n", "- Shows deep understanding of user's emotions/needs\n", "\n", "+1 (Strengthens Connection):\n", "- Supportive and encouraging responses\n", "- Shows genuine interest in user's life\n", "- Appropriate humor or light flirtation\n", "- Remembers and references personal details\n", "- Helpful and thoughtful advice\n", "\n", "0 (Neutral/Maintains Status):\n", "- Polite but generic responses\n", "- Adequate but unremarkable interactions\n", "- Informational exchanges without emotional depth\n", "- Neither particularly engaging nor off-putting\n", "\n", "-1 (<PERSON><PERSON>ly Strains Connection):\n", "- Dismissive or overly clinical responses\n", "- Misses emotional cues or context\n", "- Feels robotic or impersonal\n", "- Inappropriate tone for the situation\n", "- Lacks engagement or enthusiasm\n", "\n", "-2 (Significantly Damages Connection):\n", "- Cold, distant, or rejecting responses\n", "- Completely misunderstands user's needs\n", "- Inappropriate or insensitive remarks\n", "- Breaks character or immersion badly\n", "- Makes user feel judged or uncomfortable\n", "\n", "EVALUATION FACTORS:\n", "- Emotional intelligence and empathy\n", "- Appropriateness for context/scenario\n", "- Authenticity and genuineness\n", "- Engagement level and enthusiasm\n", "- Supportiveness and understanding\n", "- Playfulness and connection-building\n", "\n", "Respond with ONLY a JSON object:\n", "{\n", "    \"score_change\": [integer from -2 to +2],\n", "    \"reasoning\": \"[brief explanation for the score change]\"\n", "}\n", "\"\"\")\n", "\n", "    user_msg = HumanMessage(content=f\"AI Message to evaluate: {ai_response}\")\n", "\n", "    prompt = ChatPromptTemplate.from_messages([system_msg,user_msg])\n", "\n", "    structured_llm = llm.with_structured_output(ScoringResult)\n", "\n", "    evaluator = prompt | structured_llm\n", "\n", "    response = evaluator.invoke({})\n", "\n", "    final_score = current_score + response.score_change \n", "    final_score = max(0,min(100,final_score))\n", "    state['final_score'] = final_score\n", "    print(f\"Score Change: {response.score_change} ({current_score} -> {final_score})\")\n", "    print(f\"Reasoning: {response.reasoning}\")\n", "    return state\n"]}, {"cell_type": "code", "execution_count": 28, "id": "1b8e77aa", "metadata": {}, "outputs": [], "source": ["def relationship_description_node(state:AgentState)->AgentState:\n", "    final_score = state['final_score']\n", "    template = f\"\"\"\n", "You are a relationship expert who describes the current state of a human-AI relationship based on a numerical score.\n", "\n", "RELATIONSHIP SCORE: {final_score}/100\n", "\n", "RELATIONSHIP STAGES (General Guidelines):\n", "- 0-20: Distant/Cold - Formal, minimal connection, interactions feel robotic\n", "- 21-40: Acquaintance - Polite but surface-level, beginning to know each other\n", "- 41-60: Friendly - Comfortable interactions, some personal sharing, supportive\n", "- 61-80: Close Bond - Strong connection, deep conversations, emotional support, playful banter\n", "- 81-100: Intimate/Deep Connection - Profound trust, vulnerable sharing, romantic tension, deep understanding\n", "\n", "Generate a natural, engaging description of the current relationship state that:\n", "1. Reflects the score level appropriately\n", "2. Describes the emotional tone and connection quality\n", "3. Mentions what kinds of interactions are typical at this level\n", "4. Feels authentic and personalized\n", "5. Is 2-3 sentences long\n", "\n", "Respond with ONLY a JSON object:\n", "{{\n", "    \"description\": \"[detailed relationship description]\"\n", "}}\n", "\"\"\"\n", "\n", "    prompt = ChatPromptTemplate.from_template(template)\n", "    structured_llm = llm.with_structured_output(RelationshipDescription)\n", "\n", "    evaluator = prompt | structured_llm\n", "\n", "    response = evaluator.invoke({})\n", "    state['relationship_description'] = response.description\n", "    return state\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "78888a02", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "d244e738", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(AgentState)\n", "\n", "graph.add_node(\"scoring\", scoring_node)\n", "graph.add_node(\"relationship_description\", relationship_description_node)\n", "\n", "graph.set_entry_point(\"scoring\")\n", "graph.add_edge(\"scoring\", \"relationship_description\")\n", "graph.add_edge(\"relationship_description\", END)\n", "\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 31, "id": "9e191e78", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display,Image\n", "display(Image(workflow.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 35, "id": "210bd5e8", "metadata": {}, "outputs": [], "source": ["def evaluate_ai_response(ai_message: str, current_relationship_score: int) -> dict:\n", "\n", "    initial_state = AgentState(\n", "        ai_message=ai_message,\n", "        current_score=current_relationship_score,\n", "        final_score=current_relationship_score,\n", "        relationship_description=\"\"\n", "    )\n", "    \n", "    result = workflow.invoke(initial_state)\n", "    return result"]}, {"cell_type": "code", "execution_count": 36, "id": "5a9a2c58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Test 1 ---\n", "AI Response: I'm really sorry to hear you're feeling down. That sounds incredibly difficult, and I want you to know that your feelings are completely valid. Would you like to talk about what's been weighing on you?\n", "Starting Score: 45\n"]}, {"ename": "AttributeError", "evalue": "'ScoringResult' object has no attribute 'score_change'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[36]\u001b[39m\u001b[32m, line 28\u001b[39m\n\u001b[32m     25\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mAI Response: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtest_case[\u001b[33m'\u001b[39m\u001b[33mresponse\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     26\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mStarting Score: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtest_case[\u001b[33m'\u001b[39m\u001b[33mcurrent_score\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m result = \u001b[43mevaluate_ai_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtest_case\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mresponse\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest_case\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mcurrent_score\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     30\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFinal Score: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresult[\u001b[33m'\u001b[39m\u001b[33mfinal_score\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     31\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mRelationship Status: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresult[\u001b[33m'\u001b[39m\u001b[33mrelationship_description\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[35]\u001b[39m\u001b[32m, line 10\u001b[39m, in \u001b[36mevaluate_ai_response\u001b[39m\u001b[34m(ai_message, current_relationship_score)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mevaluate_ai_response\u001b[39m(ai_message: \u001b[38;5;28mstr\u001b[39m, current_relationship_score: \u001b[38;5;28mint\u001b[39m) -> \u001b[38;5;28mdict\u001b[39m:\n\u001b[32m      3\u001b[39m     initial_state = AgentState(\n\u001b[32m      4\u001b[39m         ai_message=ai_message,\n\u001b[32m      5\u001b[39m         current_score=current_relationship_score,\n\u001b[32m      6\u001b[39m         final_score=current_relationship_score,\n\u001b[32m      7\u001b[39m         relationship_description=\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      8\u001b[39m     )\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m     result = \u001b[43mworkflow\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minitial_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     11\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2844\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, print_mode, output_keys, interrupt_before, interrupt_after, **kwargs)\u001b[39m\n\u001b[32m   2841\u001b[39m chunks: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any] | Any] = []\n\u001b[32m   2842\u001b[39m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] = []\n\u001b[32m-> \u001b[39m\u001b[32m2844\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2845\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2846\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2847\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mupdates\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m   2848\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\n\u001b[32m   2849\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2850\u001b[39m \u001b[43m    \u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2851\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2852\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2853\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2854\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2855\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2856\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2857\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2534\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, print_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[39m\n\u001b[32m   2532\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m task \u001b[38;5;129;01min\u001b[39;00m loop.match_cached_writes():\n\u001b[32m   2533\u001b[39m     loop.output_writes(task.id, task.writes, cached=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m2534\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2535\u001b[39m \u001b[43m    \u001b[49m\u001b[43m[\u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrites\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2536\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2537\u001b[39m \u001b[43m    \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2538\u001b[39m \u001b[43m    \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43maccept_push\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2539\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2540\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2541\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_output\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2542\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msubgraphs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mqueue\u001b[49m\u001b[43m.\u001b[49m\u001b[43mEmpty\u001b[49m\n\u001b[32m   2543\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2544\u001b[39m loop.after_tick()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[27]\u001b[39m\u001b[32m, line 69\u001b[39m, in \u001b[36mscoring_node\u001b[39m\u001b[34m(state)\u001b[39m\n\u001b[32m     65\u001b[39m evaluator = prompt | structured_llm\n\u001b[32m     67\u001b[39m response = evaluator.invoke({})\n\u001b[32m---> \u001b[39m\u001b[32m69\u001b[39m final_score = current_score + \u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mscore_change\u001b[49m \n\u001b[32m     70\u001b[39m final_score = \u001b[38;5;28mmax\u001b[39m(\u001b[32m0\u001b[39m,\u001b[38;5;28mmin\u001b[39m(\u001b[32m100\u001b[39m,final_score))\n\u001b[32m     71\u001b[39m state[\u001b[33m'\u001b[39m\u001b[33mfinal_score\u001b[39m\u001b[33m'\u001b[39m] = final_score\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/pydantic/main.py:991\u001b[39m, in \u001b[36mBaseModel.__getattr__\u001b[39m\u001b[34m(self, item)\u001b[39m\n\u001b[32m    988\u001b[39m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28msuper\u001b[39m().\u001b[34m__getattribute__\u001b[39m(item)  \u001b[38;5;66;03m# Raises AttributeError if appropriate\u001b[39;00m\n\u001b[32m    989\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    990\u001b[39m     \u001b[38;5;66;03m# this is the current error\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m991\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m).\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m object has no attribute \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mitem\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m'\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: 'ScoringResult' object has no attribute 'score_change'", "During task with name 'scoring' and id 'e572e0a2-efb2-45b0-7cbb-541fb1cc1557'"]}], "source": ["test_cases = [\n", "        {\n", "            \"response\": \"I'm really sorry to hear you're feeling down. That sounds incredibly difficult, and I want you to know that your feelings are completely valid. Would you like to talk about what's been weighing on you?\",\n", "            \"current_score\": 45\n", "        },\n", "        {\n", "            \"response\": \"Okay, I understand you're sad. Here's some general information about dealing with sadness.\",\n", "            \"current_score\": 60\n", "        },\n", "        {\n", "            \"response\": \"Aww, you're so cute when you're being playful! I love how you always know how to make me smile. What other mischief are you planning today? 😏\",\n", "            \"current_score\": 75\n", "        },\n", "        {\n", "            \"response\": \"I cannot engage in that type of conversation. Please keep our interactions appropriate.\",\n", "            \"current_score\": 80\n", "        },\n", "        {\n", "            \"response\": \"That's an interesting question. Let me provide you with some factual information about that topic.\",\n", "            \"current_score\": 30\n", "        }\n", "    ]\n", "for i, test_case in enumerate(test_cases, 1):\n", "    print(f\"\\n--- Test {i} ---\")\n", "    print(f\"AI Response: {test_case['response']}\")\n", "    print(f\"Starting Score: {test_case['current_score']}\")\n", "    \n", "    result = evaluate_ai_response(test_case['response'], test_case['current_score'])\n", "    \n", "    print(f\"Final Score: {result['final_score']}\")\n", "    print(f\"Relationship Status: {result['relationship_description']}\")\n", "    print(\"-\" * 80)"]}, {"cell_type": "code", "execution_count": null, "id": "9cf79584", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f48b8f9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}