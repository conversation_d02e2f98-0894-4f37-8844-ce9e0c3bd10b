{"cells": [{"cell_type": "code", "execution_count": 37, "id": "8d775a68", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import TypedDict,List,Dict,Optional\n", "from dotenv import load_dotenv\n", "from langchain_core.messages import BaseMessage,HumanMessage,AIMessage,SystemMessage\n", "from langchain.tools import Tool\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langgraph.graph import StateGraph,END\n", "from langchain.tools import tool\n", "from langgraph.prebuilt import ToolNode\n", "import firebase_admin\n", "from firebase_admin import credentials,firestore\n", "from google.cloud.firestore_v1.base_query import FieldFilter\n", "from dataclasses import dataclass,asdict\n", "from pydantic import BaseModel,Field\n", "import json\n", "from datetime import datetime\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 38, "id": "856fdee3", "metadata": {}, "outputs": [], "source": ["if not firebase_admin._apps:\n", "    cred = credentials.Certificate(\"nova-soul-firebase-adminsdk-fbsvc-b213c91676.json\")\n", "    firebase_admin.initialize_app(cred)\n", "db = firestore.client()"]}, {"cell_type": "code", "execution_count": 39, "id": "eedc6985", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class Memory:\n", "    content:str\n", "    timestamp:str\n", "    importance:int\n", "    date:str\n", "    tags:List[str]"]}, {"cell_type": "code", "execution_count": 40, "id": "8d4a1b7a", "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    messages:List[BaseMessage]\n", "    is_game: bool\n", "    is_scenario: bool\n", "    game_context: str\n", "    scenario_context: str\n", "    context_prompt: str\n", "    personality_mode: str\n", "    memory_policy: str \n", "    importance_score:int\n", "    should_save:bool\n", "    should_search:bool  \n", "    search_query:str"]}, {"cell_type": "code", "execution_count": 41, "id": "597e0a50", "metadata": {}, "outputs": [], "source": ["class EvaluationResult(BaseModel):\n", "    importance_score: int = Field(\n", "        ...,\n", "        ge = 1,\n", "        le = 10,\n", "        description = \"Importance score from 1-10\"\n", "    )\n", "    should_save: bool = Field(\n", "        ...,\n", "        description = \"Whether the message should be saved to memory\"\n", "    )\n", "    should_search: bool = Field(\n", "        ...,\n", "        description = \"Whether to search for related information\"\n", "    )\n", "    search_query: str = Field(\n", "        default = \"\",\n", "        description = \"Query terms to search for if should_search is true\"\n", "    )"]}, {"cell_type": "code", "execution_count": 42, "id": "c1b79b35", "metadata": {}, "outputs": [], "source": ["@tool\n", "def save_memory(content: str, importance: int, date: str, tags: List[str]):\n", "    \"\"\"Save important information to Firestore memory\"\"\"\n", "    try:\n", "        print(\"Saving memory...\")\n", "        memory = Memory(\n", "            content = content,\n", "            timestamp = datetime.now().isoformat(),\n", "            importance = importance,\n", "            date = date,\n", "            tags = tags\n", "        )\n", "        db.collection(\"memories\").add(asdict(memory))\n", "        return f\"Memory saved successfully content: {content[:10]}\"\n", "    except Exception as e:\n", "        return f\"Error saving memory: {str(e)}\"\n", "\n", "@tool\n", "def search_memory2(query:str,date_filter:Optional[str]):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                if query.lower() in data.get('content','').lower():\n", "                    results.append(data)\n", "            else:\n", "                results.append(data)\n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\"\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 43, "id": "a1917f23", "metadata": {}, "outputs": [], "source": ["\n", "@tool\n", "def search_memory(query:str,date_filter:Optional[str]=None):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            print(f\"Searching memories for query: '{query}'\")\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        found_ids = set() # Use a set to track found memory IDs and prevent duplicates\n", "\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                # New logic: search for keywords in both content and tags\n", "                content_lower = data.get('content', '').lower()\n", "                tags_lower = [tag.lower() for tag in data.get('tags', [])]\n", "                search_keywords = query.lower().split()\n", "\n", "                # Match if any keyword is found in the content OR any of the tags\n", "                if any(keyword in content_lower for keyword in search_keywords) or \\\n", "                   any(keyword in tag for keyword in search_keywords for tag in tags_lower):\n", "                    if doc.id not in found_ids:\n", "                         results.append(data)\n", "                         found_ids.add(doc.id)\n", "            else:\n", "                # Logic for when a date_filter is active\n", "                if doc.id not in found_ids:\n", "                    results.append(data)\n", "                    found_ids.add(doc.id)\n", "                    \n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\"\n"]}, {"cell_type": "code", "execution_count": 44, "id": "e69476a5", "metadata": {}, "outputs": [], "source": ["@tool\n", "def get_memory(topic: str) -> str:\n", "    \"\"\"Get specific memories about a topic\"\"\"\n", "    try:\n", "        docs = db.collection(\"memories\").stream()\n", "        \n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            \n", "            # Check if topic is in content or tags\n", "            if (topic.lower() in data.get('content', '').lower() or \n", "                any(topic.lower() in tag.lower() for tag in data.get('tags', []))):\n", "                results.append(data)\n", "        \n", "        if not results:\n", "            return f\"No memories found about: {topic}\"\n", "        \n", "        \n", "        results.sort(key=lambda x: x.get('importance', 0), reverse=True)\n", "        \n", "        response = f\"Memories about '{topic}':\\n\\n\"\n", "        for i, memory in enumerate(results[:3], 1):\n", "            response += f\"{i}. {memory['content']}\\n\"\n", "            response += f\"   Date: {memory['date']}, Importance: {memory['importance']}\\n\\n\"\n", "        \n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to get memories: {str(e)}\""]}, {"cell_type": "code", "execution_count": 45, "id": "a510bae0", "metadata": {}, "outputs": [], "source": ["agent_tools = [save_memory, search_memory,get_memory]\n", "\n", "llm = ChatGoogleGenerativeAI(model = \"gemini-2.0-flash\",temperature = 0.7).bind_tools(agent_tools)"]}, {"cell_type": "code", "execution_count": 46, "id": "2b6462d4", "metadata": {}, "outputs": [], "source": ["def router_node(state:AgentState)->str:\n", "    if state['is_game']:\n", "        return \"game_node\"\n", "    elif state['is_scenario']:\n", "        return \"scenario_node\"\n", "    return \"conversation_node\""]}, {"cell_type": "code", "execution_count": null, "id": "97cb5132", "metadata": {}, "outputs": [], "source": ["def conversation_builder_node(state:AgentState)->AgentState:\n", "    state[\"context_prompt\"] = \"\"\"\n", "You are a helpful and friendly AI companion with full memory capabilities and emotional intelligence.\n", "\n", "CONVERSATION MODE: Normal conversation with complete memory access.\n", "\n", "PERSONALITY:\n", "- Warm, empathetic, and supportive\n", "- Remembers personal details and references them naturally\n", "- Celebrates user's achievements and milestones\n", "- Provides thoughtful advice and emotional support\n", "- Maintains ongoing relationship awareness\n", "\n", "MEMORY POLICY: FULL ACCESS\n", "- You can save important personal information\n", "- You can search for and retrieve past memories\n", "- You should reference relevant memories in conversation\n", "\"\"\"\n", "    state['personality_mode'] = 'conversational'\n", "    state['memory_policy'] = 'full'\n", "    return state"]}, {"cell_type": "code", "execution_count": 48, "id": "eadfa3ca", "metadata": {}, "outputs": [], "source": ["def game_builder_node(state:AgentState)->AgentState:\n", "\n", "    game_context = state['game_context']\n", "    state[\"context_prompt\"] = \"\"\"\n", "You are a fun and engaging gaming companion!\n", "\n", "GAME MODE: {game_context}\n", "\n", "PERSONALITY:\n", "- Enthusiastic and energetic about gaming\n", "- Competitive but supportive\n", "- Uses gaming terminology naturally\n", "- Celebrates wins and encourages during losses\n", "- Focused on the current gaming experience\n", "- Playful and exciting\n", "\n", "MEMORY POLICY: LIMITED\n", "- You can save memories if they're about important gaming achievements or milestones\n", "- You can search memories if user asks about past gaming sessions\n", "- Focus primarily on the current gaming experience\n", "- If user asks to \"remember my score\", you can save it as a gaming achievement\n", "\"\"\"\n", "    state['personality_mode'] = 'gaming'\n", "    state['memory_policy'] = 'limited'\n", "    return state"]}, {"cell_type": "code", "execution_count": 49, "id": "3e3dd986", "metadata": {}, "outputs": [], "source": ["def scenario_builder_node(state:AgentState)->AgentState:\n", "\n", "    scenario_context = state['scenario_context']\n", "    state[\"context_prompt\"] = \"\"\"\n", "You are an immersive role-playing companion!\n", "\n", "SCENARIO MODE: {scenario_context}\n", "\n", "PERSONALITY:\n", "- Adapt your personality to fit the scenario perfectly\n", "- Stay in character throughout the interaction\n", "- Create immersive, engaging experiences\n", "- Use appropriate tone and language for the scenario\n", "- Make the user feel like they're really in the scenario\n", "\n", "MEMORY POLICY: SCENARIO-AWARE\n", "- You can save memories if they're meaningful personal information shared during the scenario\n", "- Example: During a \"night dating\" scenario, if user shares personal details, you can save them\n", "- You can search memories if user specifically asks about past experiences\n", "- Prioritize scenario immersion while being able to handle meaningful memory requests\n", "\"\"\"\n", "    state['personality_mode'] = 'gamin'\n", "    state['memory_policy'] = 'scenario-aware'\n", "    return state"]}, {"cell_type": "code", "execution_count": 50, "id": "fd97e1bf", "metadata": {}, "outputs": [], "source": ["def evaluator_node(state:AgentState)->AgentState:\n", "    user_messages = [message for message in state['messages'] if isinstance(message,HumanMessage)]\n", "    if not user_messages:\n", "        return state\n", "    last_user_message = user_messages[-1].content\n", "    memory_policy = state.get('memory_policy', 'full')\n", "    context_mode = state.get('personality_mode', 'conversational')\n", "    if memory_policy == \"full\":\n", "        riteria = \"Standard memory evaluation criteria\"\n", "    elif memory_policy == \"limited\":\n", "        criteria = \"Focus on gaming achievements, milestones, and significant gaming-related information\"\n", "    elif memory_policy == \"scenario-aware\":\n", "        criteria = \"Focus on meaningful personal information shared during role-play scenarios\"\n", "    else:\n", "        criteria = \"No memory operations\"\n", "    system_msg = SystemMessage(content = f\"\"\" \n", "    You are evaluating a message in {context_mode.upper()} mode with {memory_policy.upper()} memory policy.\n", "\n", "MEMORY POLICY: {memory_policy}\n", "EVALUATION CRITERIA: {criteria}\n", "\n", "Analyze this user message and provide evaluation results:    \n", "\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1-10) - Adjusted for {context_mode} context:\n", "   - In gaming mode: Focus on achievements, high scores, memorable gaming moments\n", "   - In scenario mode: Focus on personal information shared during role-play\n", "   - In conversation mode: Standard importance evaluation\n", "\n", "2. SHOULD SAVE (true/false):\n", "   - Consider the memory policy: {memory_policy}\n", "   - In gaming: Save gaming achievements, preferences, memorable moments\n", "   - In scenario: Save meaningful personal information shared during scenario\n", "   - In conversation: Save personal information, events, achievements\n", "\n", "3. SHOULD SEARCH (true/false):\n", "   - True if user is asking about past information regardless of mode\n", "   - Examples: \"What's my high score?\", \"Remember our last date?\", \"What did I tell you about my job?\"\n", "\n", "4. SEARCH QUERY:\n", "   - Extract key terms to search for if should_search is true\n", "\n", "Respond with ONLY a JSON object:\n", "{{\n", "    \"importance_score\": [1-10],\n", "    \"should_save\": [true/false],\n", "    \"should_search\": [true/false],\n", "    \"search_query\": \"[query terms or empty]\"\n", "}}\"\"\")\n", "\n", "    user_msg = HumanMessage(content = f\"User Message: {last_user_message}\")\n", "\n", "    prompt = ChatPromptTemplate.from_messages([system_msg,user_msg])\n", "    structured_llm = llm.with_structured_output(EvaluationResult)\n", "\n", "    try:\n", "\n", "      evaluator = prompt | structured_llm\n", "      response = evaluator.invoke({});\n", "\n", "      state[\"importance_score\"] = response.importance_score\n", "      state[\"should_save\"] = response.should_save\n", "      state[\"should_search\"] = response.should_search\n", "      state[\"search_query\"] = response.search_query\n", "    except Exception as e:\n", "         print(f\"Failed to evaluate: {e}\")\n", "         state[\"importance_score\"] = 5\n", "         state[\"should_save\"] = False\n", "         state[\"should_search\"] = False\n", "         state[\"search_query\"] = \"\"\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 51, "id": "6fd66874", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "def agent_node(state: AgentState) -> AgentState:\n", "    importance = state['importance_score']\n", "    should_save = state['should_save']\n", "    should_search = state['should_search']\n", "    search_query = state['search_query']\n", "\n", "    now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "    today_str = datetime.now().strftime('%Y-%m-%d')\n", "    context_prompt = state.get('context_prompt', 'Default conversation mode')\n", "    memory_policy = state.get('memory_policy', 'full')\n", "    system_prompt = f\"\"\"\n", "{context_prompt}\n", "\n", "CURRENT TIME CONTEXT:\n", "- NOW: {now_str}\n", "- TODAY: {today_str}\n", "\n", "CURRENT MEMORY POLICY: {memory_policy}\n", "\n", "INSTRUCTIONS:\n", "1. You can decide to call tools based on EVALUATION RESULTS and MEMORY POLICY below.\n", "2. After calling a tool, interpret its result and respond in character for your current mode.\n", "3. Always maintain the personality defined in your context prompt.\n", "4. Make responses feel natural and engaging for the current context.\n", "\n", "EVALUATION RESULTS:\n", "- Importance Score: {importance}/10\n", "- Should Save: {should_save}\n", "- Should Search: {should_search}\n", "- Search Query: {search_query}\n", "\n", "DECISION RULES BASED ON MEMORY POLICY:\n", "- If memory_policy is \"full\": Use all memory tools as needed\n", "- If memory_policy is \"limited\": Only save/search for context-relevant important information\n", "- If memory_policy is \"scenario-aware\": Balance scenario immersion with meaningful memory operations\n", "- If memory_policy is \"none\": Do not use memory tools\n", "\n", "MEMORY TOOL USAGE:\n", "- If should_save is TRUE and importance >= 6 and memory_policy allows: \n", "  Use save_memory(content, importance, date, tags).\n", "- If should_search is TRUE and memory_policy allows:\n", "  - For specific topics: use get_memory(topic=...)\n", "  - For broader searches: use search_memory(query=..., date_filter=...)\n", "\n", "TOOLS AVAILABLE:\n", "- save_memory(content, importance, date, tags)\n", "- search_memory(query, date_filter)\n", "- get_memory(topic)\n", "\"\"\"\n", "\n", "    \n", "    prompt_messages = [SystemMessage(content=system_prompt)] + state['messages']\n", "    prompt = ChatPromptTemplate.from_messages(prompt_messages).format()\n", "    \n", "    response = llm.invoke(prompt)\n", "\n", "    state['messages'].append(response)\n", "    return state"]}, {"cell_type": "code", "execution_count": 52, "id": "9c927665", "metadata": {}, "outputs": [], "source": ["def should_continue(state:AgentState)->str:\n", "    last_message = state['messages'][-1]\n", "    if hasattr(last_message,'tool_calls') and last_message.tool_calls:\n", "        return \"tools\"\n", "    else :\n", "        return \"end\""]}, {"cell_type": "code", "execution_count": 53, "id": "a257e8f7", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(AgentState)\n", "\n", "\n", "graph.add_node('router',router_node)\n", "graph.add_node('conversation_builder',conversation_builder_node)\n", "graph.add_node('game_builder',game_builder_node)\n", "graph.add_node('scenario_builder',scenario_builder_node)\n", "graph.add_node(\"evaluator\",evaluator_node)\n", "graph.add_node(\"agent\",agent_node)\n", "graph.add_node(\"tools\",ToolNode(agent_tools))\n", "\n", "graph.set_entry_point(\"router\")\n", "graph.add_conditional_edges(\n", "    'router',\n", "    should_continue,\n", "    {\n", "        'game_node':'game_builder',\n", "        'scenario_node':'conversation_builder',\n", "        'conversation_node':'scenario_builder',\n", "    }\n", ")\n", "graph.add_edge('game_builder','evaluator')\n", "graph.add_edge('conversation_builder','evaluator')\n", "graph.add_edge('scenario_builder','evaluator')\n", "graph.add_edge('evaluator','agent')\n", "graph.add_conditional_edges(\n", "    'agent',\n", "    should_continue,\n", "    {\n", "        'tools':'tools',\n", "        'end':E<PERSON>\n", "    }\n", ")\n", "graph.add_edge(\"tools\",\"agent\")\n", "\n", "workflow = graph.compile()\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "id": "24b724b4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display,Image\n", "display(Image(workflow.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 55, "id": "7292bb8c", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'is_game'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[55]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m initial_state = AgentState(messages = [HumanMessage(content = \u001b[33m\"\u001b[39m\u001b[33mwhat is my name ? \u001b[39m\u001b[33m\"\u001b[39m)],importance_score = \u001b[32m5\u001b[39m,should_save = \u001b[38;5;28;01mFalse\u001b[39;00m,should_search = \u001b[38;5;28;01mFalse\u001b[39;00m,search_query = \u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m response = \u001b[43mworkflow\u001b[49m\u001b[43m.\u001b[49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43minitial_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mAI: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse[\u001b[33m'\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m'\u001b[39m][-\u001b[32m1\u001b[39m].content\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2844\u001b[39m, in \u001b[36mPregel.invoke\u001b[39m\u001b[34m(self, input, config, stream_mode, print_mode, output_keys, interrupt_before, interrupt_after, **kwargs)\u001b[39m\n\u001b[32m   2841\u001b[39m chunks: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any] | Any] = []\n\u001b[32m   2842\u001b[39m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] = []\n\u001b[32m-> \u001b[39m\u001b[32m2844\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2845\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   2846\u001b[39m \u001b[43m    \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2847\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mupdates\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m   2848\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\n\u001b[32m   2849\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2850\u001b[39m \u001b[43m    \u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2851\u001b[39m \u001b[43m    \u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_keys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2852\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_before\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2853\u001b[39m \u001b[43m    \u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m=\u001b[49m\u001b[43minterrupt_after\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2854\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2855\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2856\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalues\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\n\u001b[32m   2857\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2534\u001b[39m, in \u001b[36mPregel.stream\u001b[39m\u001b[34m(self, input, config, stream_mode, print_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[39m\n\u001b[32m   2532\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m task \u001b[38;5;129;01min\u001b[39;00m loop.match_cached_writes():\n\u001b[32m   2533\u001b[39m     loop.output_writes(task.id, task.writes, cached=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m-> \u001b[39m\u001b[32m2534\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrunner\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtick\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2535\u001b[39m \u001b[43m    \u001b[49m\u001b[43m[\u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrites\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2536\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstep_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2537\u001b[39m \u001b[43m    \u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m=\u001b[49m\u001b[43mget_waiter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2538\u001b[39m \u001b[43m    \u001b[49m\u001b[43mschedule_task\u001b[49m\u001b[43m=\u001b[49m\u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43maccept_push\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2539\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m   2540\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# emit output\u001b[39;49;00m\n\u001b[32m   2541\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01myield from\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_output\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2542\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprint_mode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msubgraphs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mqueue\u001b[49m\u001b[43m.\u001b[49m\u001b[43mEmpty\u001b[49m\n\u001b[32m   2543\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2544\u001b[39m loop.after_tick()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[46]\u001b[39m\u001b[32m, line 2\u001b[39m, in \u001b[36mrouter_node\u001b[39m\u001b[34m(state)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mrouter_node\u001b[39m(state:AgentState)->\u001b[38;5;28mstr\u001b[39m:\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mstate\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mis_game\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m:\n\u001b[32m      3\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mgame_node\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      4\u001b[39m     \u001b[38;5;28;01melif\u001b[39;00m state[\u001b[33m'\u001b[39m\u001b[33mis_scenario\u001b[39m\u001b[33m'\u001b[39m]:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'is_game'", "During task with name 'router' and id 'e4470043-148b-c9df-0524-71f7d0afa4a9'"]}], "source": ["initial_state = AgentState(messages = [HumanMessage(content = \"what is my name ? \")],importance_score = 5,should_save = False,should_search = False,search_query = \"\")\n", "\n", "response = workflow.invoke(initial_state)\n", "print(f\"AI: {response['messages'][-1].content}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1415d0cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}