{"cells": [{"cell_type": "code", "execution_count": 12, "id": "4a8356cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setup complete. Defining state and tools.\n"]}], "source": ["import os\n", "from typing import TypedDict, List, Optional\n", "from IPython.display import Image, display\n", "\n", "# --- <PERSON>/LangGraph Imports ---\n", "from langchain_core.messages import BaseMessage, HumanMessage, AIMessage\n", "from langchain_core.tools import tool\n", "from langgraph.graph import StateGraph, END, START\n", "# from langchain_google_genai import ChatGoogleGenerativeAI # We'll mock this for now\n", "\n", "# --- Set your API Key (replace with your actual key) ---\n", "# os.environ[\"GOOGLE_API_KEY\"] = \"YOUR_GOOGLE_API_KEY\"\n", "\n", "# For demonstration, we will not initialize the LLM.\n", "# In a real application, you would uncomment this:\n", "# llm = ChatGoogleGenerativeAI(model=\"gemini-pro\")\n", "\n", "print(\"Setup complete. Defining state and tools.\")"]}, {"cell_type": "code", "execution_count": 13, "id": "b3c4422b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["State and tools defined.\n", "Mock LLM logic defined.\n"]}], "source": ["# --- 1. Define the Agent State ---\n", "# This dictionary carries information between the nodes of the graph.\n", "class AgentState(TypedDict):\n", "    messages: List[BaseMessage]\n", "    is_game: bool\n", "    is_scenario: bool\n", "    game_context: Optional[str]\n", "    scenario_context: Optional[str]\n", "    personality_profile: str\n", "    generation: Optional[str]\n", "    tool_outputs: Optional[dict]\n", "    # This key will be added by some nodes to help with routing\n", "    next_node: Optional[str]\n", "\n", "# --- 2. <PERSON><PERSON> ---\n", "# In a real app, these would interact with databases, APIs, game engines, etc.\n", "@tool\n", "def save_to_long_term_memory(thing_to_remember: str) -> str:\n", "    \"\"\"Saves a piece of information to the agent's long-term memory.\"\"\"\n", "    print(f\"--- TOOL: Saving '{thing_to_remember}' to memory. ---\")\n", "    return f\"Successfully remembered: {thing_to_remember}\"\n", "\n", "@tool\n", "def search_long_term_memory(query: str) -> str:\n", "    \"\"\"Searches the agent's long-term memory for a query.\"\"\"\n", "    print(f\"--- TOOL: Searching memory for '{query}'. ---\")\n", "    return f\"Memory found for '{query}': User likes sci-fi movies.\"\n", "\n", "@tool\n", "def game_engine(move: str, context: str) -> str:\n", "    \"\"\"Processes a move in the current game.\"\"\"\n", "    print(f\"--- TOOL: Processing game move '{move}' in context '{context}'. ---\")\n", "    return f\"Game update: In response to your move '{move}', the board state is now X.\"\n", "\n", "@tool\n", "def scenario_engine(user_action: str, context: str) -> str:\n", "    \"\"\"Processes a user action in the current roleplaying scenario.\"\"\"\n", "    print(f\"--- TOOL: Processing scenario action '{user_action}' in context '{context}'. ---\")\n", "    return f\"Scenario update: The character looks surprised by your action '{user_action}'.\"\n", "\n", "print(\"State and tools defined.\")\n", "\n", "# --- 3. <PERSON><PERSON>ck LLM Logic (for routing and generation) ---\n", "# These functions simulate an LLM call to make a decision or generate text.\n", "# This makes our graphs runnable without actual API calls.\n", "\n", "def mock_llm_logic(prompt: str) -> str:\n", "    \"\"\"A generic mock LLM call.\"\"\"\n", "    print(f\"\\n>> MOCK LLM: Evaluating prompt...\\n'{prompt[:150]}...'\")\n", "    if \"triage\" in prompt:\n", "        return \"continue\" # Default for pipeline\n", "    if \"evaluate user question\" in prompt:\n", "        return \"continue_to_generation\" # Default for conversation\n", "    return \"This is a generated response based on the context.\"\n", "\n", "print(\"Mock LLM logic defined.\")"]}, {"cell_type": "code", "execution_count": 15, "id": "68f70f27", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Architecture 1: Central Router Graph ---\n", "---\n", "config:\n", "  flowchart:\n", "    curve: linear\n", "---\n", "graph TD;\n", "\t__start__([<p>__start__</p>]):::first\n", "\tgame_node(game_node)\n", "\tscenario_node(scenario_node)\n", "\tconversation_node(conversation_node)\n", "\tmemory_agent_node(memory_agent_node)\n", "\tfinal_response_node(final_response_node)\n", "\t__end__([<p>__end__</p>]):::last\n", "\t__start__ -.-> conversation_node;\n", "\t__start__ -.-> game_node;\n", "\t__start__ -.-> scenario_node;\n", "\tconversation_node -.-> final_response_node;\n", "\tconversation_node -.-> memory_agent_node;\n", "\tgame_node --> final_response_node;\n", "\tmemory_agent_node --> final_response_node;\n", "\tscenario_node --> final_response_node;\n", "\tfinal_response_node --> __end__;\n", "\tclassDef default fill:#f2f0ff,line-height:1.2\n", "\tclassDef first fill-opacity:0\n", "\tclassDef last fill:#bfb6fc\n", "\n"]}], "source": ["# --- Node Definitions for Architecture 1 ---\n", "\n", "def game_logic(state: AgentState) -> AgentState:\n", "    print(\"NODE: game_logic\")\n", "    # In a real app, would check for meta-commands first, then call game_engine\n", "    state['generation'] = \"Processed game logic. The opponent moves their knight.\"\n", "    return state\n", "\n", "def scenario_logic(state: AgentState) -> AgentState:\n", "    print(\"NODE: scenario_logic\")\n", "    state['generation'] = \"Processed scenario logic. The date smiles.\"\n", "    return state\n", "\n", "def conversational_logic(state: AgentState) -> AgentState:\n", "    print(\"NODE: conversational_logic\")\n", "    # This node decides if memory tools are needed for a general chat.\n", "    prompt = \"Evaluate user question. Does it need memory search/save? Respond 'use_memory' or 'continue_to_generation'.\"\n", "    decision = mock_llm_logic(prompt) # Simulate LLM call\n", "    state['next_node'] = decision\n", "    return state\n", "\n", "def memory_agent(state: AgentState) -> AgentState:\n", "    print(\"NODE: memory_agent\")\n", "    # Simulates calling the memory tools\n", "    state['tool_outputs'] = {\"memory_result\": \"Successfully saved to memory.\"}\n", "    return state\n", "\n", "def personality_infusion_generator(state: AgentState) -> AgentState:\n", "    print(\"NODE: personality_infusion_generator\")\n", "    # Combines all context into a final, personality-driven response.\n", "    final_response = f\"Final response infused with personality. (Context: {state.get('generation') or state.get('tool_outputs')})\"\n", "    state['messages'].append(AIMessage(content=final_response))\n", "    return state\n", "\n", "# --- Graph Definition for Architecture 1 ---\n", "\n", "# 1. Routing functions\n", "def mode_router(state: AgentState) -> str:\n", "    \"\"\"The main entry router for the graph.\"\"\"\n", "    print(\"ROUTER: mode_router\")\n", "    if state['is_game']:\n", "        return \"game_node\"\n", "    if state['is_scenario']:\n", "        return \"scenario_node\"\n", "    return \"conversation_node\"\n", "\n", "def should_use_memory(state: AgentState) -> str:\n", "    \"\"\"Router for the conversational branch.\"\"\"\n", "    print(\"ROUTER: should_use_memory\")\n", "    if state.get('next_node') == \"use_memory\":\n", "        return \"memory_agent_node\"\n", "    return \"final_response_node\"\n", "\n", "# 2. Build the graph\n", "workflow1 = StateGraph(AgentState)\n", "\n", "workflow1.add_node(\"game_node\", game_logic)\n", "workflow1.add_node(\"scenario_node\", scenario_logic)\n", "workflow1.add_node(\"conversation_node\", conversational_logic)\n", "workflow1.add_node(\"memory_agent_node\", memory_agent)\n", "workflow1.add_node(\"final_response_node\", personality_infusion_generator)\n", "\n", "# 3. Add the edges\n", "workflow1.add_conditional_edges(\n", "    START,\n", "    mode_router,\n", "    {\n", "        \"game_node\": \"game_node\",\n", "        \"scenario_node\": \"scenario_node\",\n", "        \"conversation_node\": \"conversation_node\"\n", "    }\n", ")\n", "\n", "workflow1.add_edge(\"game_node\", \"final_response_node\")\n", "workflow1.add_edge(\"scenario_node\", \"final_response_node\")\n", "workflow1.add_edge(\"memory_agent_node\", \"final_response_node\")\n", "\n", "workflow1.add_conditional_edges(\n", "    \"conversation_node\",\n", "    should_use_memory,\n", "    {\n", "        \"memory_agent_node\": \"memory_agent_node\",\n", "        \"final_response_node\": \"final_response_node\"\n", "    }\n", ")\n", "\n", "workflow1.add_edge(\"final_response_node\", END)\n", "\n", "# 4. Compile and display\n", "app1 = workflow1.compile()\n", "print(\"\\n--- Architecture 1: Central Router Graph ---\")\n", "mermaid_string = app1.get_graph().draw_mermaid()\n", "print(mermaid_string)"]}, {"cell_type": "code", "execution_count": 16, "id": "f7f27abd", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Branch with name `route_after_triage` already exists for node `triage_node`", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 66\u001b[39m\n\u001b[32m     52\u001b[39m workflow2.add_conditional_edges(\n\u001b[32m     53\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtriage_node\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     54\u001b[39m     route_after_triage,\n\u001b[32m   (...)\u001b[39m\u001b[32m     58\u001b[39m     }\n\u001b[32m     59\u001b[39m )\n\u001b[32m     61\u001b[39m \u001b[38;5;66;03m# A more accurate way to do the second conditional routing:\u001b[39;00m\n\u001b[32m     62\u001b[39m \u001b[38;5;66;03m# workflow2.add_node(\"context_router_node\", some_dummy_function)\u001b[39;00m\n\u001b[32m     63\u001b[39m \u001b[38;5;66;03m# workflow2.add_conditional_edges(\"context_router_node\", context_router, ...)\u001b[39;00m\n\u001b[32m     64\u001b[39m \u001b[38;5;66;03m# But for visualization, we'll keep it simpler. The diagram shows the intent.\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m66\u001b[39m \u001b[43mworkflow2\u001b[49m\u001b[43m.\u001b[49m\u001b[43madd_conditional_edges\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     67\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtriage_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     68\u001b[39m \u001b[43m    \u001b[49m\u001b[43mroute_after_triage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     69\u001b[39m \u001b[43m    \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m     70\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmemory_agent_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmemory_agent_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     71\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontext_router_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmode_specific_agent_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgeneral_chat_agent_node\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# Mocking the next decision\u001b[39;49;00m\n\u001b[32m     72\u001b[39m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\n\u001b[32m     73\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     76\u001b[39m workflow2.add_edge(\u001b[33m\"\u001b[39m\u001b[33mmemory_agent_node\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mfinal_response_node\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     77\u001b[39m workflow2.add_edge(\u001b[33m\"\u001b[39m\u001b[33mmode_specific_agent_node\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mfinal_response_node\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/langgraph/graph/state.py:640\u001b[39m, in \u001b[36mStateGraph.add_conditional_edges\u001b[39m\u001b[34m(self, source, path, path_map)\u001b[39m\n\u001b[32m    638\u001b[39m \u001b[38;5;66;03m# validate the condition\u001b[39;00m\n\u001b[32m    639\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.branches[source]:\n\u001b[32m--> \u001b[39m\u001b[32m640\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    641\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mBranch with name `\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath.name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` already exists for node `\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msource\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m`\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    642\u001b[39m     )\n\u001b[32m    643\u001b[39m \u001b[38;5;66;03m# save it\u001b[39;00m\n\u001b[32m    644\u001b[39m \u001b[38;5;28mself\u001b[39m.branches[source][name] = Branch.from_path(path, path_map, \u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[31mValueError\u001b[39m: Branch with name `route_after_triage` already exists for node `triage_node`"]}], "source": ["# --- Node Definitions for Architecture 2 ---\n", "\n", "def triage_for_system_commands(state: AgentState) -> AgentState:\n", "    print(\"NODE: triage_for_system_commands\")\n", "    prompt = \"Triage user input for memory commands. Respond 'memory' or 'continue'.\"\n", "    decision = mock_llm_logic(prompt)\n", "    state['next_node'] = decision\n", "    return state\n", "\n", "def mode_specific_agent(state: AgentState) -> AgentState:\n", "    print(\"NODE: mode_specific_agent\")\n", "    if state['is_game']:\n", "        state['generation'] = \"Processed game logic in mode-specific agent.\"\n", "    elif state['is_scenario']:\n", "        state['generation'] = \"Processed scenario logic in mode-specific agent.\"\n", "    return state\n", "    \n", "def general_chat_agent(state: AgentState) -> AgentState:\n", "    print(\"NODE: general_chat_agent\")\n", "    state['generation'] = \"This is a general chat response.\"\n", "    return state\n", "\n", "# We can reuse memory_agent and personality_infusion_generator from Arch 1\n", "\n", "# --- Graph Definition for Architecture 2 ---\n", "\n", "# 1. Routing Functions\n", "def route_after_triage(state: AgentState) -> str:\n", "    print(\"ROUTER: route_after_triage\")\n", "    if state.get('next_node') == \"memory\":\n", "        return \"memory_agent_node\"\n", "    return \"context_router_node\"\n", "\n", "def context_router(state: AgentState) -> str:\n", "    print(\"ROUTER: context_router\")\n", "    if state['is_game'] or state['is_scenario']:\n", "        return \"mode_specific_agent_node\"\n", "    return \"general_chat_agent_node\"\n", "\n", "# 2. Build the graph\n", "workflow2 = StateGraph(AgentState)\n", "\n", "workflow2.add_node(\"triage_node\", triage_for_system_commands)\n", "workflow2.add_node(\"memory_agent_node\", memory_agent)\n", "workflow2.add_node(\"mode_specific_agent_node\", mode_specific_agent)\n", "workflow2.add_node(\"general_chat_agent_node\", general_chat_agent)\n", "workflow2.add_node(\"final_response_node\", personality_infusion_generator)\n", "\n", "# 3. Add the edges\n", "workflow2.set_entry_point(\"triage_node\")\n", "\n", "workflow2.add_conditional_edges(\n", "    \"triage_node\",\n", "    route_after_triage,\n", "    {\n", "        \"memory_agent_node\": \"memory_agent_node\",\n", "        \"context_router_node\": \"mode_specific_agent_node\" # This is a simplification; a node could do routing\n", "    }\n", ")\n", "\n", "# A more accurate way to do the second conditional routing:\n", "# workflow2.add_node(\"context_router_node\", some_dummy_function)\n", "# workflow2.add_conditional_edges(\"context_router_node\", context_router, ...)\n", "# But for visualization, we'll keep it simpler. The diagram shows the intent.\n", "\n", "workflow2.add_conditional_edges(\n", "    \"triage_node\",\n", "    route_after_triage,\n", "    {\n", "        \"memory_agent_node\": \"memory_agent_node\",\n", "        \"context_router_node\": \"mode_specific_agent_node\" if True else \"general_chat_agent_node\" # Mocking the next decision\n", "    }\n", ")\n", "\n", "\n", "workflow2.add_edge(\"memory_agent_node\", \"final_response_node\")\n", "workflow2.add_edge(\"mode_specific_agent_node\", \"final_response_node\")\n", "workflow2.add_edge(\"general_chat_agent_node\", \"final_response_node\") # In a real graph, this would be wired correctly\n", "workflow2.add_edge(\"final_response_node\", END)\n", "\n", "\n", "# Let's fix the graph logic to be more representative\n", "workflow2_fixed = StateGraph(AgentState)\n", "workflow2_fixed.add_node(\"triage_node\", triage_for_system_commands)\n", "workflow2_fixed.add_node(\"memory_agent_node\", memory_agent)\n", "# This dummy node just holds the place for the second routing decision\n", "workflow2_fixed.add_node(\"context_router_placeholder\", lambda state: state) \n", "workflow2_fixed.add_node(\"mode_specific_agent_node\", mode_specific_agent)\n", "workflow2_fixed.add_node(\"general_chat_agent_node\", general_chat_agent)\n", "workflow2_fixed.add_node(\"final_response_node\", personality_infusion_generator)\n", "\n", "workflow2_fixed.set_entry_point(\"triage_node\")\n", "workflow2_fixed.add_conditional_edges(\"triage_node\", route_after_triage, {\n", "    \"memory_agent_node\": \"memory_agent_node\",\n", "    \"context_router_node\": \"context_router_placeholder\"\n", "})\n", "workflow2_fixed.add_conditional_edges(\"context_router_placeholder\", context_router, {\n", "    \"mode_specific_agent_node\": \"mode_specific_agent_node\",\n", "    \"general_chat_agent_node\": \"general_chat_agent_node\"\n", "})\n", "\n", "workflow2_fixed.add_edge(\"memory_agent_node\", \"final_response_node\")\n", "workflow2_fixed.add_edge(\"mode_specific_agent_node\", \"final_response_node\")\n", "workflow2_fixed.add_edge(\"general_chat_agent_node\", \"final_response_node\")\n", "workflow2_fixed.add_edge(\"final_response_node\", END)\n", "\n", "\n", "# 4. Compile and display\n", "app2 = workflow2_fixed.compile()\n", "print(\"\\n--- Architecture 2: Layered Pipeline Graph ---\")\n", "display(Image(app2.get_graph().draw_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "a18a33e2", "metadata": {}, "outputs": [], "source": ["# --- Node Definitions for Architecture 3 ---\n", "\n", "def activity_zone_agent(state: AgentState) -> AgentState:\n", "    print(\"NODE: activity_zone_agent\")\n", "    # This agent has NO access to memory tools. It just plays the game/scenario.\n", "    if state['is_game']:\n", "        state['generation'] = \"Game move processed. Memory is disabled in this zone.\"\n", "    elif state['is_scenario']:\n", "        state['generation'] = \"Scenario action processed. Memory is disabled in this zone.\"\n", "    return state\n", "    \n", "# We can reuse conversational_logic, memory_agent, and personality_infusion_generator\n", "\n", "# --- Graph Definition for Architecture 3 ---\n", "\n", "# 1. Routing Function\n", "def mode_router_gated(state: AgentState) -> str:\n", "    \"\"\"Routes to a zone with or without memory tools.\"\"\"\n", "    print(\"ROUTER: mode_router_gated\")\n", "    if state['is_game'] or state['is_scenario']:\n", "        return \"activity_zone\"\n", "    return \"conversational_zone\"\n", "\n", "# We can reuse the should_use_memory router from Arch 1\n", "\n", "# 2. Build the graph\n", "workflow3 = StateGraph(AgentState)\n", "\n", "workflow3.add_node(\"activity_zone_node\", activity_zone_agent)\n", "workflow3.add_node(\"conversational_zone_node\", conversational_logic)\n", "workflow3.add_node(\"memory_agent_node\", memory_agent)\n", "workflow3.add_node(\"final_response_node\", personality_infusion_generator)\n", "\n", "# 3. Add the edges\n", "workflow3.add_conditional_edges(\n", "    START,\n", "    mode_router_gated,\n", "    {\n", "        \"activity_zone\": \"activity_zone_node\",\n", "        \"conversational_zone\": \"conversational_zone_node\",\n", "    }\n", ")\n", "\n", "# The activity zone completely bypasses the memory branch\n", "workflow3.add_edge(\"activity_zone_node\", \"final_response_node\")\n", "\n", "# The conversational zone has the memory branch\n", "workflow3.add_conditional_edges(\n", "    \"conversational_zone_node\",\n", "    should_use_memory,\n", "    {\n", "        \"memory_agent_node\": \"memory_agent_node\",\n", "        \"final_response_node\": \"final_response_node\"\n", "    }\n", ")\n", "workflow3.add_edge(\"memory_agent_node\", \"final_response_node\")\n", "\n", "workflow3.add_edge(\"final_response_node\", END)\n", "\n", "# 4. Compile and display\n", "app3 = workflow3.compile()\n", "print(\"\\n--- Architecture 3: Context-Gated Memory Graph ---\")\n", "display(Image(app3.get_graph().draw_png()))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}