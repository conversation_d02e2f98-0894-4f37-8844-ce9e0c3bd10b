from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class Message:
    content: str
    sender: str
    datetime: datetime
    image_url: str = None

@dataclass
class Conversation:
    id: str
    user_id: str
    created_at: datetime
    companion_id: str = None

@dataclass
class User:
    id: str
    email: str
    username: str
    gender: str
    profile_image_url: str = None
    created_at: datetime = None
    updated_at: datetime = None
    is_onboarding_complete: bool = False

@dataclass
class Companion:
    id: str
    user_id: str
    name: str
    gender: str
    connection_type: str  # romantic or friendship
    personality_style: str  # normal or wildcard
    image_url: str = None
    conversation_id: str = None
    relationship_level: int = 0
    relationship_status: str = "Friendly"
    relationship_score: int = 0
    is_active: bool = False
    created_at: datetime = None
    updated_at: datetime = None
    last_interaction: datetime = None