from flask import Flask, request, jsonify
from flask_cors import CORS
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
import json
from agent import run_agent
from langchain_core.messages import HumanMessage, AIMessage
from dataclasses import dataclass, asdict
from models import Message, Conversation, Companion, User
import cloudinary
import cloudinary.uploader
import os
from relation_ship_scoring_agent import evaluate_ai_response

app = Flask(__name__)
CORS(app)

if not firebase_admin._apps:
    cred = credentials.Certificate("nova-soul-firebase-adminsdk-fbsvc-b213c91676.json")
    firebase_admin.initialize_app(cred)

db = firestore.client()

cloudinary.config(
    cloud_name = os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key = os.getenv('CLOUDINARY_API_KEY'),
    api_secret = os.getenv('CLOUDINARY_API_SECRET')
)

def create_conversation_if_needed(user_id) -> str:
    """Create a new conversation if needed and return the conversation_id"""
    try:
        from uuid import uuid4
        conversation_id = str(uuid4())

        # Create conversation document
        conversation_ref = db.collection('conversations').document(conversation_id)
        conversation_data = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
        }
        conversation_ref.set(conversation_data)

        return conversation_id
    except Exception as e:
        print(f"Error creating conversation: {e}")
        raise e

def save_message_to_firestore(message, user_id, conversation_id=None, ai=False, image_url=None) -> tuple[bool, str]:
    try:
        print(f"Attempting to save message: '{message}' for user: {user_id}, conversation: {conversation_id}, ai: {ai}")

        now_utc = datetime.now()
        message_obj = Message(
            content=message,
            datetime=now_utc,
            sender="ai" if ai else "human",
            image_url=image_url
        )

        print(f"Message object created: {asdict(message_obj)}")

        if conversation_id:
            print(f"Using existing conversation: {conversation_id}")
            conversation_ref = db.collection('conversations').document(conversation_id)
        else:
            print("Creating new conversation")
            conversation_ref = db.collection('conversations').document()
            conversation_id = conversation_ref.id
            conversation = Conversation(
                id=conversation_id,
                user_id=user_id,
                created_at=datetime.now()
            )
            print(f"Setting conversation: {asdict(conversation)}")
            conversation_ref.set(asdict(conversation))

        print(f"Adding message to conversation {conversation_id}")
        doc_ref = conversation_ref.collection('messages').add(asdict(message_obj))
        print(f"Message saved successfully with ID: {doc_ref[1].id}")
        return True, conversation_id
    except Exception as e:
        print(f"error uploading message {e}")
        import traceback
        traceback.print_exc()
        return False, conversation_id

def get_conversation_history(conversation_id: str, n: int = 4) -> list[Message]:
    try:
        print(f"Getting conversation history for: {conversation_id}")
        messages_ref = (db.collection('conversations')
            .document(conversation_id)
            .collection('messages')
            .order_by("datetime", direction=firestore.Query.DESCENDING)
            .limit(n)
            )
        docs = messages_ref.stream()
        results = []
        for doc in docs:
            data = doc.to_dict()
            print(f"Retrieved message: {data}")
            timestamp = data.get('datetime')
            if hasattr(timestamp, 'to_datetime'):
                created_at = timestamp.to_datetime()
            else:
                created_at = timestamp
            results.append(
                Message(
                    content=data.get('content'),
                    sender=data.get('sender'),
                    datetime=created_at,
                    image_url=data.get('image_url')
                )
            )
        results.reverse()
        print(f"Returning {len(results)} messages")
        return results
    except Exception as e:
        print(f"Error getting conversation history: {e}")
        import traceback
        traceback.print_exc()
        return []

def upload_image_to_cloudinary(image) -> str:
    resp = cloudinary.uploader.upload(image)
    image_url = resp.get('secure_url')
    return image_url

def get_companion_from_conversation(conversation_id: str) -> Companion:
    """Get companion information from conversation ID"""
    try:
        print(f"Getting companion for conversation: {conversation_id}")

        # First, get the conversation to find the companion_id
        conversation_ref = db.collection('conversations').document(conversation_id)
        conversation_doc = conversation_ref.get()

        if not conversation_doc.exists:
            print(f"Conversation {conversation_id} not found")
            return None

        conversation_data = conversation_doc.to_dict()
        companion_id = conversation_data.get('companion_id')

        if not companion_id:
            print(f"No companion_id found in conversation {conversation_id}")
            return None

        # Get the companion information
        companion_ref = db.collection('companions').document(companion_id)
        companion_doc = companion_ref.get()

        if not companion_doc.exists:
            print(f"Companion {companion_id} not found")
            return None

        companion_data = companion_doc.to_dict()
        print(f"Found companion: {companion_data.get('name')} - {companion_data.get('personality_style')}")

        return Companion(
            id=companion_id,
            user_id=companion_data.get('user_id', ''),
            name=companion_data.get('name', ''),
            gender=companion_data.get('gender', ''),
            connection_type=companion_data.get('connection_type', 'friendship'),
            personality_style=companion_data.get('personality_style', 'normal'),
            image_url=companion_data.get('image_url'),
            conversation_id=conversation_id,
            relationship_level=companion_data.get('relationship_level', 0),
            relationship_status=companion_data.get('relationship_status', 'Friendly'),
            relationship_score=companion_data.get('relationship_score', 0),
            is_active=companion_data.get('is_active', False),
            created_at=companion_data.get('created_at'),
            updated_at=companion_data.get('updated_at'),
            last_interaction=companion_data.get('last_interaction')
        )

    except Exception as e:
        print(f"Error getting companion: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_user_by_id(user_id: str) -> User:
    """Get user information by user ID"""
    try:
        print(f"Getting user information for: {user_id}")

        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            print(f"User {user_id} not found")
            return None

        user_data = user_doc.to_dict()
        print(f"Found user: {user_data.get('username')} - {user_data.get('gender')}")

        # Parse timestamps
        created_at = user_data.get('created_at')
        if hasattr(created_at, 'to_datetime'):
            created_at = created_at.to_datetime()

        updated_at = user_data.get('updated_at')
        if hasattr(updated_at, 'to_datetime'):
            updated_at = updated_at.to_datetime()

        return User(
            id=user_data.get('id'),
            email=user_data.get('email', ''),
            username=user_data.get('username', ''),
            gender=user_data.get('gender', ''),
            profile_image_url=user_data.get('profile_image_url'),
            created_at=created_at,
            updated_at=updated_at,
            is_onboarding_complete=user_data.get('is_onboarding_complete', False)
        )

    except Exception as e:
        print(f"Error getting user: {e}")
        return None

def update_companion_relationship(companion_id: str, new_score: int, relationship_description: str = None):
    """Update companion relationship score and derived fields"""
    try:
        print(f"Updating companion {companion_id} with new score: {new_score}")

        # Get current companion data
        companion_ref = db.collection('companions').document(companion_id)
        companion_doc = companion_ref.get()

        if not companion_doc.exists:
            print(f"Companion {companion_id} not found")
            return False

        companion_data = companion_doc.to_dict()

        # Calculate relationship level (0-7 based on score ranges)
        relationship_level = calculate_relationship_level(new_score)

        # Determine relationship status based on score and personality
        personality_style = companion_data.get('personality_style', 'normal')
        connection_type = companion_data.get('connection_type', 'friendship')
        relationship_status = calculate_relationship_status(new_score, personality_style, connection_type)

        # Update companion data
        update_data = {
            'relationship_score': new_score,
            'relationship_level': relationship_level,
            'relationship_status': relationship_status,
            'last_interaction': datetime.now(),
            'updated_at': datetime.now()
        }

        if relationship_description:
            update_data['relationship_description'] = relationship_description

        companion_ref.update(update_data)

        print(f"Updated companion {companion_id}: score={new_score}, level={relationship_level}, status={relationship_status}")
        return True

    except Exception as e:
        print(f"Error updating companion relationship: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_relationship_level(score: int) -> int:
    """Calculate relationship level (0-7) based on score (0-100)"""
    if score <= 12:
        return 0  # Stranger
    elif score <= 25:
        return 1  # Acquaintance
    elif score <= 37:
        return 2  # Friend
    elif score <= 50:
        return 3  # Close Friend
    elif score <= 62:
        return 4  # Best Friend
    elif score <= 75:
        return 5  # Romantic Interest
    elif score <= 87:
        return 6  # Partner
    else:
        return 7  # Soulmate

def calculate_relationship_status(score: int, personality_style: str, connection_type: str) -> str:
    """Calculate relationship status based on score, personality, and connection type"""

    # Base status categories based on score ranges
    if score <= 20:
        base_statuses = ["Cold", "Distant", "Formal"]
    elif score <= 40:
        base_statuses = ["Polite", "Cautious", "Reserved"]
    elif score <= 60:
        base_statuses = ["Friendly", "Warm", "Comfortable"]
    elif score <= 80:
        base_statuses = ["Close", "Affectionate", "Playful"]
    else:
        base_statuses = ["Intimate", "Passionate", "Devoted"]

    # Modify based on personality style
    if personality_style == "wildcard":
        if score <= 20:
            wildcard_statuses = ["Sassy", "Mysterious", "Unpredictable"]
        elif score <= 40:
            wildcard_statuses = ["Teasing", "Quirky", "Intriguing"]
        elif score <= 60:
            wildcard_statuses = ["Playful", "Flirty", "Spontaneous"]
        elif score <= 80:
            wildcard_statuses = ["Passionate", "Bold", "Adventurous"]
        else:
            wildcard_statuses = ["Wild", "Intense", "Magnetic"]
        base_statuses.extend(wildcard_statuses)

    # Modify based on connection type
    if connection_type == "romantic" and score > 40:
        if score <= 60:
            romantic_statuses = ["Flirty", "Sweet", "Charming"]
        elif score <= 80:
            romantic_statuses = ["Loving", "Romantic", "Tender"]
        else:
            romantic_statuses = ["Passionate", "Adoring", "Soulful"]
        base_statuses.extend(romantic_statuses)

    # Return the first status (could be randomized in the future)
    return base_statuses[0]
        


@app.route('/send_message', methods=['POST'])
def send_message():
    try:
        # Handle both JSON and form data
        if request.content_type and 'application/json' in request.content_type:
            data = request.get_json()
        else:
            # Handle form data (multipart/form-data)
            data = request.form.to_dict()

        message = data.get('message', '')  # Allow empty message
        user_id = data.get('user_id')

        # Handle boolean conversion for form data
        is_game = data.get('is_game', False)
        if isinstance(is_game, str):
            is_game = is_game.lower() in ('true', '1', 'yes')

        is_scenario = data.get('is_scenario', False)
        if isinstance(is_scenario, str):
            is_scenario = is_scenario.lower() in ('true', '1', 'yes')

        selected_game = data.get('selected_game', '')
        selected_scenario = data.get('selected_scenario', '')
        conversation_id = data.get('conversation_id', None)

        # Debug logging
        print(f"Received data: message='{message}', is_game={is_game}, is_scenario={is_scenario}, selected_game='{selected_game}', selected_scenario='{selected_scenario}'")

        # Check if this is a scenario/game activation
        is_activation = (message in ["__SCENARIO_ACTIVATION__", "__GAME_ACTIVATION__"]) or ((not message) and (selected_game or selected_scenario))
        print(f"is_activation: {is_activation}")

        if not user_id:
            return jsonify({"error": "user id is required"}), 400

        image_url = None
        if 'image' in request.files:
            image = request.files['image']
            image_url = upload_image_to_cloudinary(image)

        # Validate that we have either message, image, or scenario/game activation
        if not message and not image_url and not is_activation:
            return jsonify({"error": "Either message, image, or scenario/game selection is required"}), 400

        # Determine the message to send to agent
        agent_message = message
        if not message and image_url:
            agent_message = "describe this image"
        elif is_activation:
            agent_message = "Let's start"

        # Save user message only if it's not a scenario/game activation
        if not is_activation:
            user_message_saved, conversation_id = save_message_to_firestore(
                message=message,
                user_id=user_id,
                conversation_id=conversation_id,
                image_url=image_url
            )
            if not user_message_saved:
                return jsonify({"error": "error when saving user message"}), 500
        else:
            # For activation, we still need a conversation_id, so create one if needed
            if not conversation_id:
                conversation_id = create_conversation_if_needed(user_id)

        user_history = get_conversation_history(conversation_id=conversation_id)

        # Get companion information for personality context
        companion = get_companion_from_conversation(conversation_id)

        if companion is None:
            return jsonify({"error": "Companion not found for conversation"}), 400

        # Get user information for context
        user = get_user_by_id(user_id)

        if user is None:
            return jsonify({"error": "User not found"}), 400

        # Add the current message with image URL to history for agent
        current_message = Message(
            content=agent_message,
            sender="human",
            datetime=datetime.now(),
            image_url=image_url
        )
        user_history.append(current_message)

        # Get user's subscription tier
        subscription_tier = 'FREE'  # Default to FREE
        try:
            user_doc = db.collection('users').document(user_id).get()
            if user_doc.exists:
                user_data = user_doc.to_dict()
                subscription_tier = user_data.get('subscription_tier', 'FREE')
        except Exception as e:
            print(f"Error fetching user subscription tier: {e}")

        agent_response = run_agent(
            messages=user_history,
            is_game=is_game,
            selected_game=selected_game,
            is_scenario=is_scenario,
            selected_scenario=selected_scenario,
            companion=companion,
            user=user,
            subscription_tier=subscription_tier
        )

        # Extract AI message content from the response
        ai_message_content = ""
        if agent_response and 'messages' in agent_response and agent_response['messages']:
            last_message = agent_response['messages'][-1]
            if hasattr(last_message, 'content'):
                ai_message_content = last_message.content
            else:
                ai_message_content = str(last_message)
        else:
            ai_message_content = "I'm sorry, I couldn't process that request right now."

        # Save AI response to Firestore
        agent_message_saved, _ = save_message_to_firestore(
            message=ai_message_content,
            user_id=user_id,
            conversation_id=conversation_id,
            ai=True
        )
        if not agent_message_saved:
            return jsonify({"error": "error when saving agent message"}), 500

        # Calculate relationship score using the scoring agent
        try:
            current_score = companion.relationship_score
            print(f"Current relationship score: {current_score}")

            # Get last 10 messages for context
            last_messages = []
            try:
                # Try with 'datetime' field first (as used in other parts of the code)
                messages_query = db.collection('conversations').document(conversation_id).collection('messages').order_by('datetime', direction=firestore.Query.DESCENDING).limit(10)
                messages_docs = messages_query.get()
                last_messages = [doc.to_dict().get('content', '') for doc in messages_docs if doc.to_dict().get('content')]
                last_messages.reverse()  # Reverse to get chronological order
                print(f"Retrieved {len(last_messages)} messages for context")
            except Exception as e:
                print(f"Error fetching last messages with 'datetime' field: {e}")
                # Fallback to 'timestamp' field
                try:
                    messages_query = db.collection('conversations').document(conversation_id).collection('messages').order_by('timestamp', direction=firestore.Query.DESCENDING).limit(10)
                    messages_docs = messages_query.get()
                    last_messages = [doc.to_dict().get('content', '') for doc in messages_docs if doc.to_dict().get('content')]
                    last_messages.reverse()  # Reverse to get chronological order
                    print(f"Retrieved {len(last_messages)} messages for context (using timestamp)")
                except Exception as e2:
                    print(f"Error fetching last messages with 'timestamp' field: {e2}")
                    # If both fail, try without ordering
                    try:
                        messages_query = db.collection('conversations').document(conversation_id).collection('messages').limit(10)
                        messages_docs = messages_query.get()
                        last_messages = [doc.to_dict().get('content', '') for doc in messages_docs if doc.to_dict().get('content')]
                        print(f"Retrieved {len(last_messages)} messages for context (no ordering)")
                    except Exception as e3:
                        print(f"Error fetching messages without ordering: {e3}")
                        last_messages = []

            # Get companion data from Firestore for personality and relationship type
            try:
                companion_ref = db.collection('companions').document(companion.id)
                companion_doc = companion_ref.get()
                if companion_doc.exists:
                    companion_data = companion_doc.to_dict()
                    personality = companion_data.get('personality_style', 'caring')
                    relationship_type = companion_data.get('connection_type', 'friendly')
                else:
                    print(f"Warning: Companion document not found for ID: {companion.id}")
                    personality = 'caring'
                    relationship_type = 'friendly'
            except Exception as e:
                print(f"Error fetching companion data: {e}")
                personality = 'caring'
                relationship_type = 'friendly'

            print(f"Companion personality: {personality}, relationship_type: {relationship_type}")

            # Evaluate AI response for relationship impact with enhanced context
            print(f"Evaluating AI response: '{ai_message_content[:100]}...'")
            scoring_result = evaluate_ai_response(
                ai_message_content,
                current_score,
                last_messages=last_messages,
                relationship_type=relationship_type,
                personality=personality
            )

            new_score = scoring_result.get('final_score', current_score)
            relationship_description = scoring_result.get('relationship_description', '')
            insights = scoring_result.get('insights', [])

            print(f"Relationship scoring result: {current_score} -> {new_score}")
            print(f"Relationship description: {relationship_description}")
            print(f"Generated {len(insights)} insights")

            # Log insights details for debugging
            for i, insight in enumerate(insights):
                print(f"Insight {i+1}: {insight.get('title', 'No title')} - {insight.get('category', 'No category')}")

            # Always return insights, even if score doesn't change
            # This ensures insights are generated and stored for every interaction
            update_success = True
            if new_score != current_score:
                update_success = update_companion_relationship(
                    companion.id,
                    new_score,
                    relationship_description
                )
                if not update_success:
                    print("Warning: Failed to update companion relationship")
            else:
                print("Score unchanged, but still returning insights")

            # Return response with relationship update info and insights
            return jsonify({
                "conversation_id": conversation_id,
                "response": {
                    "messages": [{"content": ai_message_content, "sender": "ai"}]
                },
                "relationship_update": {
                    "previous_score": current_score,
                    "new_score": new_score,
                    "score_change": new_score - current_score,
                    "description": relationship_description,
                    "insights": insights
                }
            }), 200

        except Exception as scoring_error:
            print(f"Error in relationship scoring: {scoring_error}")
            import traceback
            traceback.print_exc()
            # Continue without scoring if there's an error
            return jsonify({
                "conversation_id": conversation_id,
                "response": {
                    "messages": [{"content": ai_message_content, "sender": "ai"}]
                },
            }), 200

    except Exception as e:
        print(f"/send_message error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "Nova Soul Backend is running"}), 200


@app.route('/create_test_companion', methods=['POST'])
def create_test_companion():
    """Create a test companion for testing purposes"""
    try:
        companion_id = "test_companion_123"
        companion_data = {
            "id": companion_id,
            "user_id": "test123",
            "name": "Emma",
            "gender": "female",
            "connection_type": "romantic",
            "personality_style": "wildcard",
            "image_url": None,
            "conversation_id": None,
            "relationship_level": 3,
            "relationship_status": "Flirty",
            "relationship_score": 65,
            "is_active": True,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "last_interaction": None
        }

        # Save companion to Firestore
        db.collection('companions').document(companion_id).set(companion_data)

        return jsonify({"message": "Test companion created", "companion_id": companion_id}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/create_conversation_with_companion', methods=['POST'])
def create_conversation_with_companion():
    """Create a new conversation with a companion"""
    try:
        data = request.get_json() if request.content_type and 'application/json' in request.content_type else request.form.to_dict()

        user_id = data.get('user_id', 'test123')
        companion_id = data.get('companion_id', 'test_companion_123')

        # Create new conversation with companion_id
        conversation_ref = db.collection('conversations').document()
        conversation_id = conversation_ref.id

        conversation_data = {
            "id": conversation_id,
            "user_id": user_id,
            "companion_id": companion_id,
            "created_at": datetime.now()
        }

        conversation_ref.set(conversation_data)

        return jsonify({
            "message": "Conversation created with companion",
            "conversation_id": conversation_id,
            "companion_id": companion_id
        }), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)




