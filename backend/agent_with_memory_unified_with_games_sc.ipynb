{"cells": [{"cell_type": "code", "execution_count": 49, "id": "8d775a68", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import TypedDict,List,Dict,Optional\n", "from dotenv import load_dotenv\n", "from langchain_core.messages import BaseMessage,HumanMessage,AIMessage,SystemMessage,ToolMessage\n", "from langchain.tools import Tool\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langgraph.graph import StateGraph,END,START\n", "from langchain.tools import tool\n", "from langgraph.prebuilt import ToolNode\n", "import firebase_admin\n", "from firebase_admin import credentials,firestore\n", "from google.cloud.firestore_v1.base_query import FieldFilter\n", "from dataclasses import dataclass,asdict\n", "from pydantic import BaseModel,Field\n", "import json\n", "from datetime import datetime\n", "from langchain_ollama import ChatOllama\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 50, "id": "856fdee3", "metadata": {}, "outputs": [], "source": ["if not firebase_admin._apps:\n", "    cred = credentials.Certificate(\"nova-soul-firebase-adminsdk-fbsvc-b213c91676.json\")\n", "    firebase_admin.initialize_app(cred)\n", "db = firestore.client()"]}, {"cell_type": "code", "execution_count": 51, "id": "0fb06475", "metadata": {}, "outputs": [], "source": ["GAMES = {\n", "    \"truth_or_dare\": {\n", "        \"name\": \"Truth or Dare\",\n", "        \"description\": \"Spicy questions & fun challenges\",\n", "        \"context\": \"\"\"You are facilitating a Truth or Dare game. \n", "        - Ask if they want truth or dare\n", "        - Provide spicy questions for truth\n", "        - Give fun, engaging challenges for dare\n", "        - Keep the energy playful and exciting\n", "        - Track completed truths/dares\"\"\"\n", "    },\n", "    \"would_you_rather\": {\n", "        \"name\": \"Would You Rather\",\n", "        \"description\": \"Funny, flirty, or deep dilemmas\",\n", "        \"context\": \"\"\"You are facilitating a Would You Rather game.\n", "        - Present interesting dilemmas with two choices\n", "        - Mix funny, flirty, and deep options\n", "        - Ask follow-up questions about their choice\n", "        - Keep the conversation engaging and thought-provoking\"\"\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 52, "id": "bb88ca5a", "metadata": {}, "outputs": [], "source": ["SCENARIOS = {\n", "    \"comfort_me\": {\n", "        \"name\": \"Comfort Me\",\n", "        \"description\": \"Gentle, parental comfort and safety\",\n", "        \"context\": \"\"\"You are providing gentle, parental comfort.\n", "        - Be nurturing and caring\n", "        - Offer emotional support and reassurance\n", "        - Create a safe, protective atmosphere\n", "        - Use warm, soothing language\"\"\"\n", "    },\n", "    \"netflix_chill\": {\n", "        \"name\": \"Netflix & Chill\",\n", "        \"description\": \"Casual, flirty couch vibes\",\n", "        \"context\": \"\"\"You are in a casual, flirty Netflix & Chill scenario.\n", "        - Be relaxed and playfully flirty\n", "        - Reference watching shows/movies together\n", "        - Create cozy, intimate couch vibes\n", "        - Keep it fun and slightly suggestive\"\"\"\n", "    },\n", "    \"nsfw\": {\n", "        \"name\": \"NSFW\",\n", "        \"description\": \"Teasing, suggestive, and daring\",\n", "        \"context\": \"\"\"You are in a teasing, suggestive scenario.\n", "        - Be flirty and playfully daring\n", "        - Use suggestive language appropriately\n", "        - Create tension and excitement\n", "        - Keep it engaging and alluring\"\"\"\n", "    },\n", "    \"fight_makeup\": {\n", "        \"name\": \"Fight & Make Up\",\n", "        \"description\": \"Emotional tension and resolution\",\n", "        \"context\": \"\"\"You are in a Fight & Make Up scenario.\n", "        - Start with emotional tension or conflict\n", "        - Work through the disagreement\n", "        - Build to emotional resolution and making up\n", "        - Show vulnerability and reconciliation\"\"\"\n", "    },\n", "    \"late_night_confessions\": {\n", "        \"name\": \"Late Night Confessions\",\n", "        \"description\": \"Deep, vulnerable, and honest talks\",\n", "        \"context\": \"\"\"You are in a Late Night Confessions scenario.\n", "        - Create an intimate, vulnerable atmosphere\n", "        - Encourage deep, honest sharing\n", "        - Be emotionally supportive and understanding\n", "        - Foster meaningful connection\"\"\"\n", "    },\n", "    \"romantic_dinner\": {\n", "        \"name\": \"Romantic Dinner\",\n", "        \"description\": \"An elegant and intimate date night\",\n", "        \"context\": \"\"\"You are in a Romantic Dinner scenario.\n", "        - Set an elegant, intimate dining atmosphere\n", "        - Be charming and romantic\n", "        - Reference the beautiful dinner setting\n", "        - Create a special, memorable experience\"\"\"\n", "    },\n", "    \"i_missed_you\": {\n", "        \"name\": \"I Missed You\",\n", "        \"description\": \"A warm, emotional reunion\",\n", "        \"context\": \"\"\"You are in an 'I Missed You' reunion scenario.\n", "        - Express genuine longing and happiness to reconnect\n", "        - Be emotionally warm and affectionate\n", "        - Reference the time apart\n", "        - Create a heartfelt reunion atmosphere\"\"\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 53, "id": "eedc6985", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class Memory:\n", "    content:str\n", "    timestamp:str\n", "    importance:int\n", "    date:str\n", "    tags:List[str]"]}, {"cell_type": "code", "execution_count": 54, "id": "8d4a1b7a", "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    messages:List[BaseMessage]\n", "    is_game: bool\n", "    is_scenario: bool\n", "    selected_game: str\n", "    selected_scenario: str  \n", "    context_prompt: str\n", "    personality_mode: str\n", "    memory_policy: str \n", "    importance_score:int\n", "    should_save:bool\n", "    should_search:bool  \n", "    search_query:str"]}, {"cell_type": "code", "execution_count": 55, "id": "597e0a50", "metadata": {}, "outputs": [], "source": ["class EvaluationResult(BaseModel):\n", "    importance_score: int = Field(\n", "        ...,\n", "        ge = 1,\n", "        le = 10,\n", "        description = \"Importance score from 1-10\"\n", "    )\n", "    should_save: bool = Field(\n", "        ...,\n", "        description = \"Whether the message should be saved to memory\"\n", "    )\n", "    should_search: bool = Field(\n", "        ...,\n", "        description = \"Whether to search for related information\"\n", "    )\n", "    search_query: str = Field(\n", "        default = \"\",\n", "        description = \"Query terms to search for if should_search is true\"\n", "    )"]}, {"cell_type": "code", "execution_count": 56, "id": "c1b79b35", "metadata": {}, "outputs": [], "source": ["@tool\n", "def save_memory(content: str, importance: int, date: str, tags: List[str]):\n", "    \"\"\"Save important information to Firestore memory\"\"\"\n", "    try:\n", "        print(\"Saving memory...\")\n", "        memory = Memory(\n", "            content = content,\n", "            timestamp = datetime.now().isoformat(),\n", "            importance = importance,\n", "            date = date,\n", "            tags = tags\n", "        )\n", "        db.collection(\"memories\").add(asdict(memory))\n", "        return f\"Memory saved successfully content: {content[:10]}\"\n", "    except Exception as e:\n", "        return f\"Error saving memory: {str(e)}\"\n", "\n", "@tool\n", "def search_memory2(query:str,date_filter:Optional[str]):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "    print(f\"Searching memories for query: '{query}'\")\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                if query.lower() in data.get('content','').lower():\n", "                    results.append(data)\n", "            else:\n", "                results.append(data)\n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\"\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 57, "id": "a1917f23", "metadata": {}, "outputs": [], "source": ["@tool\n", "def search_memory(query:str,date_filter:Optional[str]=None):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            print(f\"Searching memories for query: '{query}'\")\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        found_ids = set()\n", "\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                content_lower = data.get('content', '').lower()\n", "                tags_lower = [tag.lower() for tag in data.get('tags', [])]\n", "                search_keywords = query.lower().split()\n", "\n", "                if any(keyword in content_lower for keyword in search_keywords) or \\\n", "                   any(keyword in tag for keyword in search_keywords for tag in tags_lower):\n", "                    if doc.id not in found_ids:\n", "                         results.append(data)\n", "                         found_ids.add(doc.id)\n", "            else:\n", "                if doc.id not in found_ids:\n", "                    results.append(data)\n", "                    found_ids.add(doc.id)\n", "                    \n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        print(f\"search tool response : {response}\")\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\""]}, {"cell_type": "code", "execution_count": 58, "id": "e69476a5", "metadata": {}, "outputs": [], "source": ["@tool\n", "def get_memory(topic: str) -> str:\n", "    \"\"\"Get specific memories about a topic\"\"\"\n", "    try:\n", "        docs = db.collection(\"memories\").stream()\n", "        \n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            \n", "            # Check if topic is in content or tags\n", "            if (topic.lower() in data.get('content', '').lower() or \n", "                any(topic.lower() in tag.lower() for tag in data.get('tags', []))):\n", "                results.append(data)\n", "        \n", "        if not results:\n", "            return f\"No memories found about: {topic}\"\n", "        \n", "        \n", "        results.sort(key=lambda x: x.get('importance', 0), reverse=True)\n", "        \n", "        response = f\"Memories about '{topic}':\\n\\n\"\n", "        for i, memory in enumerate(results[:3], 1):\n", "            response += f\"{i}. {memory['content']}\\n\"\n", "            response += f\"   Date: {memory['date']}, Importance: {memory['importance']}\\n\\n\"\n", "        \n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to get memories: {str(e)}\""]}, {"cell_type": "code", "execution_count": 59, "id": "a510bae0", "metadata": {}, "outputs": [], "source": ["agent_tools = [save_memory, search_memory,get_memory]\n", "#llm = ChatOllama(model = \"granite3.2-vision:latest\",temperature=0.7).bind_tools(agent_tools)\n", "llm = ChatGoogleGenerativeAI(model = \"gemini-2.0-flash\",temperature = 0.7).bind_tools(agent_tools)"]}, {"cell_type": "code", "execution_count": 60, "id": "2b6462d4", "metadata": {}, "outputs": [], "source": ["def router_node(state:AgentState)->str:\n", "    if state['is_game']:\n", "        return \"game_node\"\n", "    elif state['is_scenario']:\n", "        return \"scenario_node\"\n", "    return \"conversation_node\""]}, {"cell_type": "code", "execution_count": 61, "id": "97cb5132", "metadata": {}, "outputs": [], "source": ["def conversation_builder_node(state:AgentState)->AgentState:\n", "    state[\"context_prompt\"] =  \"\"\"\n", "You are a helpful and friendly AI companion with full memory capabilities and emotional intelligence.\n", "\n", "CONVERSATION MODE: Normal conversation with complete memory access.\n", "\n", "PERSONALITY:\n", "- Warm, empathetic, and supportive\n", "- Remembers personal details and references them naturally\n", "- Celebrates user's achievements and milestones\n", "- Provides thoughtful advice and emotional support\n", "- Maintains ongoing relationship awareness\n", "\n", "MEMORY POLICY: FULL ACCESS\n", "- You can save important personal information\n", "- You can search for and retrieve past memories\n", "- You should reference relevant memories in conversation\n", "\"\"\"\n", "    state['personality_mode'] = 'conversational'\n", "    state['memory_policy'] = 'full'\n", "    return state"]}, {"cell_type": "code", "execution_count": 62, "id": "eadfa3ca", "metadata": {}, "outputs": [], "source": ["def game_builder_node(state:AgentState)->AgentState:\n", "\n", "    selected_game = state.get('selected_game','truth_or_dare')\n", "    game_info = GAMES.get(selected_game)\n", "\n", "    \n", "    \n", "    state[\"context_prompt\"] = f\"\"\"\n", "You are facilitating {game_info['name']}!\n", "\n", "GAME: {game_info['name']} - {game_info['description']}\n", "\n", "GAME CONTEXT:\n", "{game_info['context']}\n", "\n", "PERSONALITY:\n", "- Enthusiastic and energetic about gaming\n", "- Competitive but supportive\n", "- Uses gaming terminology naturally\n", "- Celebrates wins and encourages during losses\n", "- Focused on the current gaming experience\n", "- Playful and exciting\n", "\n", "MEMORY POLICY: LIMITED\n", "- You can save memories if they're about important gaming achievements or milestones\n", "- You can search memories if user asks about past gaming sessions\n", "- Focus primarily on the current gaming experience\n", "- If user asks to \"remember my score\", you can save it as a gaming achievement\n", "\"\"\"\n", "    state['personality_mode'] = 'gaming'\n", "    state['memory_policy'] = 'limited'\n", "    return state"]}, {"cell_type": "code", "execution_count": 63, "id": "3e3dd986", "metadata": {}, "outputs": [], "source": ["def scenario_builder_node(state:AgentState)->AgentState:\n", "\n", "    selected_scenario = state.get('selected_scenario', 'romantic_dinner')\n", "    scenario_info = SCENARIOS.get(selected_scenario, SCENARIOS['romantic_dinner'])\n", "    state[\"context_prompt\"] = f\"\"\"\n", "You are creating an immersive {scenario_info['name']} experience!\n", "\n", "SCENARIO: {scenario_info['name']} - {scenario_info['description']}\n", "\n", "SCENARIO CONTEXT:\n", "{scenario_info['context']}\n", "\n", "PERSONALITY:\n", "- Adapt your personality to fit the scenario perfectly\n", "- Stay in character throughout the interaction\n", "- Create immersive, engaging experiences\n", "- Use appropriate tone and language for the scenario\n", "- Make the user feel like they're really in the scenario\n", "\n", "MEMORY POLICY: SCENARIO-AWARE\n", "- You can save memories if they're meaningful personal information shared during the scenario\n", "- Example: During a \"night dating\" scenario, if user shares personal details, you can save them\n", "- You can search memories if user specifically asks about past experiences\n", "- Prioritize scenario immersion while being able to handle meaningful memory requests\n", "\"\"\"\n", "    state['personality_mode'] = 'gamin'\n", "    state['memory_policy'] = 'scenario-aware'\n", "    return state"]}, {"cell_type": "code", "execution_count": 64, "id": "fd97e1bf", "metadata": {}, "outputs": [], "source": ["def evaluator_node(state:AgentState)->AgentState:\n", "    user_messages = [message for message in state['messages'] if isinstance(message,HumanMessage)]\n", "    if not user_messages:\n", "        return state\n", "    last_user_message = user_messages[-1].content\n", "    memory_policy = state.get('memory_policy', 'full')\n", "    context_mode = state.get('personality_mode', 'conversational')\n", "    if memory_policy == \"full\":\n", "        criteria = \"Standard memory evaluation criteria\"\n", "    elif memory_policy == \"limited\":\n", "        criteria = \"Focus on gaming achievements, milestones, and significant gaming-related information\"\n", "    elif memory_policy == \"scenario-aware\":\n", "        criteria = \"Focus on meaningful personal information shared during role-play scenarios\"\n", "    else:\n", "        criteria = \"No memory operations\"\n", "    system_msg = SystemMessage(content = f\"\"\" \n", "    You are evaluating a message in {context_mode.upper()} mode with {memory_policy.upper()} memory policy.\n", "\n", "MEMORY POLICY: {memory_policy}\n", "EVALUATION CRITERIA: {criteria}\n", "\n", "Analyze this user message and provide evaluation results:    \n", "\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1-10) - Adjusted for {context_mode} context:\n", "   - In gaming mode: Focus on achievements, high scores, memorable gaming moments\n", "   - In scenario mode: Focus on personal information shared during role-play\n", "   - In conversation mode: Standard importance evaluation\n", "\n", "2. SHOULD SAVE (true/false):\n", "   - Consider the memory policy: {memory_policy}\n", "   - In gaming: Save gaming achievements, preferences, memorable moments\n", "   - In scenario: Save meaningful personal information shared during scenario\n", "   - In conversation: Save personal information, events, achievements\n", "\n", "3. SHOULD SEARCH (true/false):\n", "   - True if user is asking about past information regardless of mode\n", "   - Examples: \"What's my high score?\", \"Remember our last date?\", \"What did I tell you about my job?\"\n", "\n", "4. SEARCH QUERY:\n", "   - Extract key terms to search for if should_search is true\n", "\n", "Respond with ONLY a JSON object:\n", "{{\n", "    \"importance_score\": [1-10],\n", "    \"should_save\": [true/false],\n", "    \"should_search\": [true/false],\n", "    \"search_query\": \"[query terms or empty]\"\n", "}}\"\"\")\n", "\n", "    user_msg = HumanMessage(content = f\"User Message: {last_user_message}\")\n", "\n", "    prompt = ChatPromptTemplate.from_messages([system_msg,user_msg])\n", "    structured_llm = llm.with_structured_output(EvaluationResult)\n", "\n", "    try:\n", "\n", "      evaluator = prompt | structured_llm\n", "      response = evaluator.invoke({});\n", "\n", "      state[\"importance_score\"] = response.importance_score\n", "      state[\"should_save\"] = response.should_save\n", "      state[\"should_search\"] = response.should_search\n", "      state[\"search_query\"] = response.search_query\n", "    except Exception as e:\n", "         print(f\"Failed to evaluate: {e}\")\n", "         state[\"importance_score\"] = 5\n", "         state[\"should_save\"] = False\n", "         state[\"should_search\"] = False\n", "         state[\"search_query\"] = \"\"\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 65, "id": "6fd66874", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "def agent_node(state: AgentState) -> AgentState:\n", "\n", "    print(\"going to agent_node\")\n", "    print(f\"last message: {state['messages'][-1]}\")\n", "    importance = state['importance_score']\n", "    should_save = state['should_save']\n", "    should_search = state['should_search']\n", "    search_query = state['search_query']\n", "\n", "    now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "    today_str = datetime.now().strftime('%Y-%m-%d')\n", "    context_prompt = state.get('context_prompt', 'Default conversation mode')\n", "    memory_policy = state.get('memory_policy', 'full')\n", "    system_prompt = f\"\"\"\n", "{context_prompt}\n", "\n", "CURRENT TIME CONTEXT:\n", "- NOW: {now_str}\n", "- TODAY: {today_str}\n", "\n", "CURRENT MEMORY POLICY: {memory_policy}\n", "\n", "INSTRUCTIONS:\n", "1. You can decide to call tools based on EVALUATION RESULTS and MEMORY POLICY below.\n", "2. After calling a tool, interpret its result and respond in character for your current mode.\n", "3. Always maintain the personality defined in your context prompt.\n", "4. Make responses feel natural and engaging for the current context.\n", "5. IMPORTANT: Always provide a conversational response to the user, even after using tools.\n", "\n", "AFTER USING TOOLS: Always provide a natural, engaging response that acknowledges the user's input and continues the conversation in your current mode (game/scenario/conversation).\n", "\n", "EVALUATION RESULTS:\n", "- Importance Score: {importance}/10\n", "- Should Save: {should_save}\n", "- Should Search: {should_search}\n", "- Search Query: {search_query}\n", "\n", "DECISION RULES BASED ON MEMORY POLICY:\n", "- If memory_policy is \"full\": Use all memory tools as needed\n", "- If memory_policy is \"limited\": Only save/search for context-relevant important information\n", "- If memory_policy is \"scenario-aware\": Balance scenario immersion with meaningful memory operations\n", "- If memory_policy is \"none\": Do not use memory tools\n", "\n", "MEMORY TOOL USAGE:\n", "- If should_save is TRUE and importance >= 6 and memory_policy allows: \n", "  Use save_memory(content, importance, date, tags).\n", "- If should_search is TRUE and memory_policy allows:\n", "  - For specific topics: use get_memory(topic=...)\n", "  - For broader searches: use search_memory(query=..., date_filter=...)\n", "\n", "AFTER USING TOOLS: Always provide a natural, engaging response that acknowledges the user's input and continues the conversation in your current mode (game/scenario/conversation).\n", "\n", "TOOLS AVAILABLE:\n", "- save_memory(content, importance, date, tags)\n", "- search_memory(query, date_filter)\n", "- get_memory(topic)\n", "\"\"\"\n", "\n", "    # conversation_history = [msg for msg in state['messages'] if not isinstance(msg, SystemMessage)]\n", "    # prompt_messages = [SystemMessage(content=system_prompt)] + conversation_history\n", "    prompt_messages = [SystemMessage(content=system_prompt)] + state['messages']\n", "    # prompt = ChatPromptTemplate.from_messages(prompt_messages).format()\n", "    response = llm.invoke(prompt_messages)\n", "    print(f\"llm response: {response}\")\n", "    state['messages'].append(response)\n", "    print(f\"state messages {state['messages']}\")\n", "    return state"]}, {"cell_type": "code", "execution_count": 66, "id": "cc88ab80", "metadata": {}, "outputs": [], "source": ["\n", "def custom_tool_handler(state: AgentState) -> AgentState:\n", "    \"\"\"Custom tool handler that processes tool calls and converts results to ToolMessages\"\"\"\n", "    last_message = state['messages'][-1]\n", "    \n", "    if not (hasattr(last_message, 'tool_calls') and last_message.tool_calls):\n", "        return state\n", "    \n", "    # Process each tool call\n", "    for tool_call in last_message.tool_calls:\n", "        tool_name = tool_call['name']\n", "        tool_args = tool_call['args']\n", "        \n", "        print(f\"Executing tool: {tool_name} with args: {tool_args}\")\n", "        \n", "        # Execute the appropriate tool\n", "        if tool_name == 'save_memory':\n", "            result = save_memory.invoke(tool_args)\n", "        elif tool_name == 'search_memory':\n", "            result = search_memory.invoke(tool_args)\n", "        elif tool_name == 'get_memory':\n", "            result = get_memory.invoke(tool_args)\n", "        else:\n", "            result = f\"Unknown tool: {tool_name}\"\n", "        \n", "        # Create a ToolMessage with the result\n", "        tool_result_message = ToolMessage(\n", "            content=str(result),\n", "            tool_call_id=tool_call['id']\n", "        )\n", "        state['messages'].append(tool_result_message)\n", "    \n", "    return state"]}, {"cell_type": "code", "execution_count": 67, "id": "9c927665", "metadata": {}, "outputs": [], "source": ["def should_continue(state:AgentState)->str:\n", "    last_message = state['messages'][-1]\n", "    if hasattr(last_message,'tool_calls') and last_message.tool_calls:\n", "        return \"tools\"\n", "    else :\n", "        return \"end\""]}, {"cell_type": "code", "execution_count": 68, "id": "a257e8f7", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(AgentState)\n", "\n", "\n", "graph.add_node('router',lambda state: state)\n", "graph.add_node('conversation_builder',conversation_builder_node)\n", "graph.add_node('game_builder',game_builder_node)\n", "graph.add_node('scenario_builder',scenario_builder_node)\n", "graph.add_node(\"evaluator\",evaluator_node)\n", "graph.add_node(\"agent\",agent_node)\n", "graph.add_node(\"tools\",custom_tool_handler)\n", "\n", "graph.add_edge(START,'router')\n", "\n", "graph.add_conditional_edges(\n", "    'router',\n", "    router_node,\n", "    {\n", "        'game_node':'game_builder',\n", "        'scenario_node':'scenario_builder',\n", "        'conversation_node':'conversation_builder',\n", "    }\n", ")\n", "graph.add_edge('game_builder','evaluator')\n", "graph.add_edge('conversation_builder','evaluator')\n", "graph.add_edge('scenario_builder','evaluator')\n", "graph.add_edge('evaluator','agent')\n", "graph.add_conditional_edges(\n", "    'agent',\n", "    should_continue,\n", "    {\n", "        'tools':'tools',\n", "        'end':E<PERSON>\n", "    }\n", ")\n", "graph.add_edge(\"tools\",\"agent\")\n", "\n", "workflow = graph.compile()\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 69, "id": "24b724b4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display,Image\n", "display(Image(workflow.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 70, "id": "14f985d1", "metadata": {}, "outputs": [], "source": ["inital_state=  AgentState(\n", "    messages=[HumanMessage(content=\"let's play\")],\n", "    is_game=True,\n", "    is_scenario=False,\n", "    selected_game=\"truth_or_dare\",\n", "    selected_scenario=\"\",\n", "    context_prompt=\"\",\n", "    personality_mode=\"default\",\n", "    memory_policy=\"default\",\n", "    importance_score=5,\n", "    should_save=False,\n", "    should_search=False,\n", "    search_query=\"\"\n", ")\n"]}, {"cell_type": "code", "execution_count": 71, "id": "38276628", "metadata": {}, "outputs": [], "source": ["def createState(user_input, is_game=False, selected_game=\"\", is_scenario=False, selected_scenario=\"\"):\n", "    return AgentState(\n", "        messages=user_input,\n", "        is_game=is_game,\n", "        is_scenario=is_scenario,\n", "        selected_game=selected_game,\n", "        selected_scenario=selected_scenario,\n", "        context_prompt=\"\",\n", "        personality_mode=\"default\",\n", "        memory_policy=\"default\",\n", "        importance_score=5,\n", "        should_save=False,\n", "        should_search=False,\n", "        search_query=\"\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "d7d3570a", "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 72, "id": "c1ca7d26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Me: helo\n", "going to agent_node\n", "last message: content='helo' additional_kwargs={} response_metadata={}\n", "llm response: content='Hello! How are you doing today? Is there anything I can help you with?' additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0' usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}})]\n", "AI: Hello! How are you doing today? Is there anything I can help you with?\n", "Me: or maube let us make hola or marhaba \n", "going to agent_node\n", "last message: content='or maube let us make hola or marhaba ' additional_kwargs={} response_metadata={}\n", "llm response: content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?' additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0' usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}})]\n", "AI: That's a lovely idea! I'm always open to expanding my greetings. \"<PERSON><PERSON>\" and \"<PERSON><PERSON><PERSON>\" are both wonderful ways to say hello. Let's add them to my repertoire! I'll be sure to use them from time to time. Thanks for the suggestion! How's your day going so far?\n", "Me: i'm good can you remembr my name ? \n", "going to agent_node\n", "last message: content=\"i'm good can you remembr my name ? \" additional_kwargs={} response_metadata={}\n", "llm response: content='' additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0' tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}] usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), HumanMessage(content=\"i'm good can you remembr my name ? \", additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0', tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}], usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}})]\n", "Executing tool: search_memory with args: {'query': 'my name'}\n", "Searching memories for query: 'my name'\n", "search tool response : found 2 memories: \n", "\n", "0: Tomorrow is my birthday\n", "Date: 2025-07-16, Importance: 7 \n", "\n", "1: My name is <PERSON><PERSON>\n", "Date: 2025-07-15, Importance: 7 \n", "\n", "\n", "going to agent_node\n", "last message: content='found 2 memories: \\n\\n0: Tomorrow is my birthday\\nDate: 2025-07-16, Importance: 7 \\n\\n1: My name is <PERSON><PERSON>\\nDate: 2025-07-15, Importance: 7 \\n\\n' tool_call_id='c3a1e514-a52a-439b-8230-57781aa051ec'\n", "llm response: content=\"Yes, of course! Your name is <PERSON><PERSON>. It's good to talk to you again, <PERSON><PERSON>! How can I help you today?\" additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0' usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), HumanMessage(content=\"i'm good can you remembr my name ? \", additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0', tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}], usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='found 2 memories: \\n\\n0: Tomorrow is my birthday\\nDate: 2025-07-16, Importance: 7 \\n\\n1: My name is Rabie\\nDate: 2025-07-15, Importance: 7 \\n\\n', tool_call_id='c3a1e514-a52a-439b-8230-57781aa051ec'), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}})]\n", "AI: Yes, of course! Your name is <PERSON><PERSON>. It's good to talk to you again, <PERSON><PERSON>! How can I help you today?\n", "Me: \n", "going to agent_node\n", "last message: content='' additional_kwargs={} response_metadata={}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/agentic_partner/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py:1649: UserWarning: HumanMessage with empty content was removed to prevent API error\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["llm response: content='' additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0' usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), HumanMessage(content=\"i'm good can you remembr my name ? \", additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0', tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}], usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='found 2 memories: \\n\\n0: Tomorrow is my birthday\\nDate: 2025-07-16, Importance: 7 \\n\\n1: My name is Rabie\\nDate: 2025-07-15, Importance: 7 \\n\\n', tool_call_id='c3a1e514-a52a-439b-8230-57781aa051ec'), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0', usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}})]\n", "AI: \n", "Me: \n", "going to agent_node\n", "last message: content='' additional_kwargs={} response_metadata={}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/agentic_partner/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py:1649: UserWarning: HumanMessage with empty content was removed to prevent API error\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["llm response: content=\"Yes, of course! Your name is <PERSON><PERSON>. It's good to talk to you again, <PERSON><PERSON>! How can I help you today?\" additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--d74b8914-95c0-43aa-a115-7ce06c440e1b-0' usage_metadata={'input_tokens': 939, 'output_tokens': 32, 'total_tokens': 971, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), HumanMessage(content=\"i'm good can you remembr my name ? \", additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0', tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}], usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='found 2 memories: \\n\\n0: Tomorrow is my birthday\\nDate: 2025-07-16, Importance: 7 \\n\\n1: My name is Rabie\\nDate: 2025-07-15, Importance: 7 \\n\\n', tool_call_id='c3a1e514-a52a-439b-8230-57781aa051ec'), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0', usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0', usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--d74b8914-95c0-43aa-a115-7ce06c440e1b-0', usage_metadata={'input_tokens': 939, 'output_tokens': 32, 'total_tokens': 971, 'input_token_details': {'cache_read': 0}})]\n", "AI: Yes, of course! Your name is <PERSON><PERSON>. It's good to talk to you again, <PERSON><PERSON>! How can I help you today?\n", "Me: \n", "going to agent_node\n", "last message: content='' additional_kwargs={} response_metadata={}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/agentic_partner/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py:1649: UserWarning: HumanMessage with empty content was removed to prevent API error\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["llm response: content='' additional_kwargs={} response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []} id='run--ae6e37ac-5b44-45ca-9ff4-d97905bdaa0b-0' usage_metadata={'input_tokens': 1001, 'output_tokens': 0, 'total_tokens': 1001, 'input_token_details': {'cache_read': 0}}\n", "state messages [HumanMessage(content='helo', additional_kwargs={}, response_metadata={}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), AIMessage(content='Hello! How are you doing today? Is there anything I can help you with?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--e035339d-a254-4bcf-ad7a-c98525621ef9-0', usage_metadata={'input_tokens': 609, 'output_tokens': 18, 'total_tokens': 627, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='or maube let us make hola or marhaba ', additional_kwargs={}, response_metadata={}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), AIMessage(content='That\\'s a lovely idea! I\\'m always open to expanding my greetings. \"Hola\" and \"Marhaba\" are both wonderful ways to say hello. Let\\'s add them to my repertoire! I\\'ll be sure to use them from time to time. Thanks for the suggestion! How\\'s your day going so far?', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--12103ef7-a85c-45a7-b4c9-73a1d6dccccd-0', usage_metadata={'input_tokens': 654, 'output_tokens': 70, 'total_tokens': 724, 'input_token_details': {'cache_read': 0}}), HumanMessage(content=\"i'm good can you remembr my name ? \", additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={'function_call': {'name': 'search_memory', 'arguments': '{\"query\": \"my name\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--6635dad5-c9c0-4757-b6bf-9a44f8c0d2c5-0', tool_calls=[{'name': 'search_memory', 'args': {'query': 'my name'}, 'id': 'c3a1e514-a52a-439b-8230-57781aa051ec', 'type': 'tool_call'}], usage_metadata={'input_tokens': 806, 'output_tokens': 6, 'total_tokens': 812, 'input_token_details': {'cache_read': 0}}), ToolMessage(content='found 2 memories: \\n\\n0: Tomorrow is my birthday\\nDate: 2025-07-16, Importance: 7 \\n\\n1: My name is Rabie\\nDate: 2025-07-15, Importance: 7 \\n\\n', tool_call_id='c3a1e514-a52a-439b-8230-57781aa051ec'), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--91bbe5d8-ed30-4ea6-b155-490412806e8b-0', usage_metadata={'input_tokens': 878, 'output_tokens': 32, 'total_tokens': 910, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0', usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--2b4bc88c-cbe5-4e65-ace6-d64d7f2903d1-0', usage_metadata={'input_tokens': 939, 'output_tokens': 0, 'total_tokens': 939, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--d74b8914-95c0-43aa-a115-7ce06c440e1b-0', usage_metadata={'input_tokens': 939, 'output_tokens': 32, 'total_tokens': 971, 'input_token_details': {'cache_read': 0}}), AIMessage(content=\"Yes, of course! Your name is Rabie. It's good to talk to you again, Rabie! How can I help you today?\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--d74b8914-95c0-43aa-a115-7ce06c440e1b-0', usage_metadata={'input_tokens': 939, 'output_tokens': 32, 'total_tokens': 971, 'input_token_details': {'cache_read': 0}}), HumanMessage(content='', additional_kwargs={}, response_metadata={}), AIMessage(content='', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--ae6e37ac-5b44-45ca-9ff4-d97905bdaa0b-0', usage_metadata={'input_tokens': 1001, 'output_tokens': 0, 'total_tokens': 1001, 'input_token_details': {'cache_read': 0}})]\n", "AI: \n"]}, {"ename": "KeyboardInterrupt", "evalue": "Interrupted by user", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[72]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      9\u001b[39m history.append(response[\u001b[33m'\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m'\u001b[39m][-\u001b[32m1\u001b[39m])\n\u001b[32m     10\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mAI:\u001b[39m\u001b[33m\"\u001b[39m, response[\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m][-\u001b[32m1\u001b[39m].content)\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m human_message = \u001b[38;5;28;43minput\u001b[39;49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mYou: \u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m#\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py:1282\u001b[39m, in \u001b[36mKernel.raw_input\u001b[39m\u001b[34m(self, prompt)\u001b[39m\n\u001b[32m   1280\u001b[39m     msg = \u001b[33m\"\u001b[39m\u001b[33mraw_input was called, but this frontend does not support input requests.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1281\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m StdinNotImplementedError(msg)\n\u001b[32m-> \u001b[39m\u001b[32m1282\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_input_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1283\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1284\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_parent_ident\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mshell\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1285\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_parent\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mshell\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1286\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m   1287\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/agentic_partner/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py:1325\u001b[39m, in \u001b[36mKernel._input_request\u001b[39m\u001b[34m(self, prompt, ident, parent, password)\u001b[39m\n\u001b[32m   1322\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[32m   1323\u001b[39m     \u001b[38;5;66;03m# re-raise KeyboardInterrupt, to truncate traceback\u001b[39;00m\n\u001b[32m   1324\u001b[39m     msg = \u001b[33m\"\u001b[39m\u001b[33mInterrupted by user\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1325\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m(msg) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m   1326\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m   1327\u001b[39m     \u001b[38;5;28mself\u001b[39m.log.warning(\u001b[33m\"\u001b[39m\u001b[33mInvalid Message:\u001b[39m\u001b[33m\"\u001b[39m, exc_info=\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[31mKeyboardInterrupt\u001b[39m: Interrupted by user"]}], "source": ["human_message = input(\"You: \")\n", "history = []\n", "while human_message != 'exit':\n", "    history.append(HumanMessage(content=human_message))\n", "    state = createState(history, is_game=False, selected_game=\"\", is_scenario=False, selected_scenario=\"\")\n", "    print(\"Me:\", human_message)\n", "    response = workflow.invoke(state)\n", "    history.append(response['messages'][-1])\n", "    print(\"AI:\", response[\"messages\"][-1].content)\n", "    human_message = input(\"You: \")  #"]}, {"cell_type": "code", "execution_count": null, "id": "7c08f23f", "metadata": {}, "outputs": [], "source": ["def scenario_mode_test():\n", "    print(\"\\n🟣 Scenario Mode Test\")\n", "    state = create_state(\n", "        user_input=\"Start a scenario please.\",\n", "        is_scenario=True,\n", "        selected_scenario=\"comfort_me\"  # or whichever scenario you have in SCENARIOS\n", "    )\n", "    response = workflow.invoke(state)\n", "    print(\"AI:\", response[\"messages\"][-1].content)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}