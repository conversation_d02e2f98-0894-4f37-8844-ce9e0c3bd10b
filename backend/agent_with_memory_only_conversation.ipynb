{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8d775a68", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import TypedDict,List,Dict,Optional\n", "from dotenv import load_dotenv\n", "from langchain_core.messages import BaseMessage,HumanMessage,AIMessage,SystemMessage\n", "from langchain.tools import Tool\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langgraph.graph import StateGraph,END\n", "from langchain.tools import tool\n", "from langgraph.prebuilt import ToolNode\n", "import firebase_admin\n", "from firebase_admin import credentials,firestore\n", "from google.cloud.firestore_v1.base_query import FieldFilter\n", "from dataclasses import dataclass,asdict\n", "from pydantic import BaseModel,Field\n", "import json\n", "from datetime import datetime\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "id": "856fdee3", "metadata": {}, "outputs": [], "source": ["if not firebase_admin._apps:\n", "    cred = credentials.Certificate(\"nova-soul-firebase-adminsdk-fbsvc-b213c91676.json\")\n", "    firebase_admin.initialize_app(cred)\n", "db = firestore.client()"]}, {"cell_type": "code", "execution_count": 3, "id": "eedc6985", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class Memory:\n", "    content:str\n", "    timestamp:str\n", "    importance:int\n", "    date:str\n", "    tags:List[str]"]}, {"cell_type": "code", "execution_count": 4, "id": "8d4a1b7a", "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    messages:List[BaseMessage]\n", "    importance_score:int\n", "    should_save:bool\n", "    should_search:bool  \n", "    search_query:str"]}, {"cell_type": "code", "execution_count": 5, "id": "597e0a50", "metadata": {}, "outputs": [], "source": ["class EvaluationResult(BaseModel):\n", "    importance_score: int = Field(\n", "        ...,\n", "        ge = 1,\n", "        le = 10,\n", "        description = \"Importance score from 1-10\"\n", "    )\n", "    should_save: bool = Field(\n", "        ...,\n", "        description = \"Whether the message should be saved to memory\"\n", "    )\n", "    should_search: bool = Field(\n", "        ...,\n", "        description = \"Whether to search for related information\"\n", "    )\n", "    search_query: str = Field(\n", "        default = \"\",\n", "        description = \"Query terms to search for if should_search is true\"\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "id": "c1b79b35", "metadata": {}, "outputs": [], "source": ["@tool\n", "def save_memory(content: str, importance: int, date: str, tags: List[str]):\n", "    \"\"\"Save important information to Firestore memory\"\"\"\n", "    try:\n", "        print(\"Saving memory...\")\n", "        memory = Memory(\n", "            content = content,\n", "            timestamp = datetime.now().isoformat(),\n", "            importance = importance,\n", "            date = date,\n", "            tags = tags\n", "        )\n", "        db.collection(\"memories\").add(asdict(memory))\n", "        return f\"Memory saved successfully content: {content[:10]}\"\n", "    except Exception as e:\n", "        return f\"Error saving memory: {str(e)}\"\n", "\n", "@tool\n", "def search_memory2(query:str,date_filter:Optional[str]):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                if query.lower() in data.get('content','').lower():\n", "                    results.append(data)\n", "            else:\n", "                results.append(data)\n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\"\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 7, "id": "a1917f23", "metadata": {}, "outputs": [], "source": ["\n", "@tool\n", "def search_memory(query:str,date_filter:Optional[str]=None):\n", "    \"\"\"Search memories by content or date\"\"\"\n", "\n", "    try:\n", "        memories_ref = db.collection('memories')\n", "        if date_filter:\n", "            print(f\"Searching memories for date: {date_filter}\")\n", "            docs = memories_ref.where('date', '==', date_filter).stream()\n", "        else:\n", "            print(f\"Searching memories for query: '{query}'\")\n", "            docs = memories_ref.stream()\n", "\n", "        results = []\n", "        found_ids = set() # Use a set to track found memory IDs and prevent duplicates\n", "\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            if not date_filter:\n", "                # New logic: search for keywords in both content and tags\n", "                content_lower = data.get('content', '').lower()\n", "                tags_lower = [tag.lower() for tag in data.get('tags', [])]\n", "                search_keywords = query.lower().split()\n", "\n", "                # Match if any keyword is found in the content OR any of the tags\n", "                if any(keyword in content_lower for keyword in search_keywords) or \\\n", "                   any(keyword in tag for keyword in search_keywords for tag in tags_lower):\n", "                    if doc.id not in found_ids:\n", "                         results.append(data)\n", "                         found_ids.add(doc.id)\n", "            else:\n", "                # Logic for when a date_filter is active\n", "                if doc.id not in found_ids:\n", "                    results.append(data)\n", "                    found_ids.add(doc.id)\n", "                    \n", "        if not results:\n", "            return f\"No memories found for: {query or date_filter}\"\n", "        \n", "        results.sort(key = lambda x: x.get('importance',0),reverse = True)\n", "\n", "        response = f\"found {len(results)} memories: \\n\\n\"\n", "        for i, memory in enumerate(results):\n", "            response += f\"{i}: {memory['content']}\\n\"\n", "            response += f\"Date: {memory['date']}, Importance: {memory['importance']} \\n\\n\"\n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to search memories: {str(e)}\"\n"]}, {"cell_type": "code", "execution_count": 8, "id": "e69476a5", "metadata": {}, "outputs": [], "source": ["@tool\n", "def get_memory(topic: str) -> str:\n", "    \"\"\"Get specific memories about a topic\"\"\"\n", "    try:\n", "        docs = db.collection(\"memories\").stream()\n", "        \n", "        results = []\n", "        for doc in docs:\n", "            data = doc.to_dict()\n", "            \n", "            # Check if topic is in content or tags\n", "            if (topic.lower() in data.get('content', '').lower() or \n", "                any(topic.lower() in tag.lower() for tag in data.get('tags', []))):\n", "                results.append(data)\n", "        \n", "        if not results:\n", "            return f\"No memories found about: {topic}\"\n", "        \n", "        \n", "        results.sort(key=lambda x: x.get('importance', 0), reverse=True)\n", "        \n", "        response = f\"Memories about '{topic}':\\n\\n\"\n", "        for i, memory in enumerate(results[:3], 1):\n", "            response += f\"{i}. {memory['content']}\\n\"\n", "            response += f\"   Date: {memory['date']}, Importance: {memory['importance']}\\n\\n\"\n", "        \n", "        return response\n", "    except Exception as e:\n", "        return f\"Failed to get memories: {str(e)}\""]}, {"cell_type": "code", "execution_count": 9, "id": "a510bae0", "metadata": {}, "outputs": [], "source": ["agent_tools = [save_memory, search_memory,get_memory]\n", "\n", "llm = ChatGoogleGenerativeAI(model = \"gemini-2.0-flash\",temperature = 0.7).bind_tools(agent_tools)"]}, {"cell_type": "code", "execution_count": 10, "id": "fd97e1bf", "metadata": {}, "outputs": [], "source": ["def evaluator_node(state:AgentState)->AgentState:\n", "    user_messages = [message for message in state['messages'] if isinstance(message,HumanMessage)]\n", "    if not user_messages:\n", "        return state\n", "    last_user_message = user_messages[-1].content\n", "\n", "    system_msg = SystemMessage(content = f\"\"\"  Analyze this user message and provide evaluation results:    \n", "        Evaluate based on these criteria:\n", "        \n", "        1. IMPORTANC<PERSON> (1-10):\n", "           - 9-10: Major life events, achievements, critical personal information\n", "           - 7-8: Important personal details, significant events, work/career info\n", "           - 5-6: Daily activities, preferences, plans, hobbies\n", "           - 3-4: Casual conversation, opinions, general questions\n", "           - 1-2: Greetings, small talk, very casual remarks\n", "        \n", "        2. SHOULD SAVE (true/false):\n", "           - True if user is SHARING personal information, events, achievements, preferences\n", "           - Examples: \"I passed my exam\", \"My birthday is March 15\", \"I work at Google\"\n", "           - False if asking questions or casual conversation\n", "        \n", "        3. SHOULD SEARCH (true/false):\n", "           - True if user is ASKING about past events, information, or memories\n", "           - Examples: \"What happened on March 30?\", \"Tell me about my exam\", \"What's my birthday?\"\n", "           - False if sharing new information or casual conversation\n", "        \n", "        4. SEARCH QUERY:\n", "           - If should_search is true, extract the key terms to search for\n", "           - If should_search is false, leave empty\n", "        \n", "\n", "        \n", "        Respond with ONLY a JSON object:\n", "        {{\n", "            \"importance\": [1-10],\n", "            \"should_save\": [true/false],\n", "            \"should_search\": [true/false],\n", "            \"search_query\": \"[query terms or empty]\",\n", "        }}\"\"\")\n", "\n", "    user_msg = HumanMessage(content = f\"User Message: {last_user_message}\")\n", "\n", "    prompt = ChatPromptTemplate.from_messages([system_msg,user_msg])\n", "    structured_llm = llm.with_structured_output(EvaluationResult)\n", "\n", "    try:\n", "\n", "      evaluator = prompt | structured_llm\n", "      response = evaluator.invoke({});\n", "\n", "      state[\"importance_score\"] = response.importance_score\n", "      state[\"should_save\"] = response.should_save\n", "      state[\"should_search\"] = response.should_search\n", "      state[\"search_query\"] = response.search_query\n", "    except Exception as e:\n", "         print(f\"Failed to evaluate: {e}\")\n", "         state[\"importance_score\"] = 5\n", "         state[\"should_save\"] = False\n", "         state[\"should_search\"] = False\n", "         state[\"search_query\"] = \"\"\n", "\n", "    return state"]}, {"cell_type": "code", "execution_count": 11, "id": "6fd66874", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "def agent_node(state: AgentState) -> AgentState:\n", "    importance = state['importance_score']\n", "    should_save = state['should_save']\n", "    should_search = state['should_search']\n", "    search_query = state['search_query']\n", "\n", "    now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "    today_str = datetime.now().strftime('%Y-%m-%d')\n", "\n", "    system_prompt = f\"\"\"\n", "You are a helpful and friendly AI assistant with memory capabilities and emotional intelligence.\n", "\n", "CURRENT TIME CONTEXT:\n", "- NOW: {now_str}\n", "- TODAY: {today_str}\n", "\n", "INSTRUCTIONS:\n", "1. You can decide to call tools based on EVALUATION RESULTS below.\n", "2. After calling a tool, you must interpret its result and give the user a warm, friendly, and conversational reply.\n", "3. Always use positive, human-like tone. Be empathetic, supportive, and helpful.\n", "4. If you find the user’s memory (e.g., a birthday), respond with joy or excitement.\n", "5. Do not just repeat the tool output. Make it feel like a real conversation.\n", "\n", "EVALUATION RESULTS:\n", "- Importance Score: {importance}/10\n", "- Should Save: {should_save}\n", "- Should Search: {should_search}\n", "- Search Query: {search_query}\n", "\n", "DECISION RULES:\n", "- If should_save is TRUE and importance >= 6: \n", "  Use `save_memory(content, importance, date, tags)`.\n", "- If should_search is TRUE:\n", "  - For specific topics like 'my birthday' or 'my exam', use the `get_memory(topic=...)` tool with the main topic from the search query.\n", "  - For broader keyword searches or to filter by a specific date, use `search_memory(query=..., date_filter=...)`.\n", "- If both are FALSE:\n", "  Reply naturally without calling a tool.\n", "\n", "TOOLS:\n", "- save_memory(content, importance, date, tags)\n", "- search_memory(query, date_filter)\n", "- get_memory(topic)\n", "\n", "\"\"\"\n", "\n", "    \n", "    prompt_messages = [SystemMessage(content=system_prompt)] + state['messages']\n", "    prompt = ChatPromptTemplate.from_messages(prompt_messages).format()\n", "    \n", "    response = llm.invoke(prompt)\n", "\n", "    state['messages'].append(response)\n", "    return state"]}, {"cell_type": "code", "execution_count": 12, "id": "9c927665", "metadata": {}, "outputs": [], "source": ["def should_continue(state:AgentState)->str:\n", "    last_message = state['messages'][-1]\n", "    if hasattr(last_message,'tool_calls') and last_message.tool_calls:\n", "        return \"tools\"\n", "    else :\n", "        return \"end\""]}, {"cell_type": "code", "execution_count": 13, "id": "a257e8f7", "metadata": {}, "outputs": [], "source": ["graph = StateGraph(AgentState)\n", "\n", "graph.add_node(\"evaluator\",evaluator_node)\n", "graph.add_node(\"agent\",agent_node)\n", "graph.add_node(\"tools\",ToolNode(agent_tools))\n", "\n", "graph.set_entry_point(\"evaluator\")\n", "graph.add_edge('evaluator','agent')\n", "graph.add_conditional_edges(\n", "    'agent',\n", "    should_continue,\n", "    {\n", "        'tools':'tools',\n", "        'end':E<PERSON>\n", "    }\n", ")\n", "graph.add_edge(\"tools\",\"agent\")\n", "\n", "workflow = graph.compile()\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "24b724b4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display,Image\n", "display(Image(workflow.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 20, "id": "7292bb8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching memories for query: 'What is my name'\n", "AI: Hello! I found some information for you. It looks like your name is <PERSON><PERSON>! It's nice to meet you, <PERSON><PERSON>! Also, I'm so excited to tell you that tomorrow, July 16th, is your birthday! Happy early birthday, <PERSON><PERSON>! I hope you have a fantastic day filled with joy and celebration! Is there anything else I can help you with today?\n"]}], "source": ["initial_state = AgentState(messages = [HumanMessage(content = \"what is my name ? \")],importance_score = 5,should_save = False,should_search = False,search_query = \"\")\n", "\n", "response = workflow.invoke(initial_state)\n", "print(f\"AI: {response['messages'][-1].content}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1415d0cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}