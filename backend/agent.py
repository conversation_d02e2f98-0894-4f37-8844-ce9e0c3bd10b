from typing import TypedDict, List, Dict, Optional
from dotenv import load_dotenv
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain.tools import Tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import StateGraph, END, START
from langchain.tools import tool
from langgraph.prebuilt import ToolNode
import firebase_admin
from firebase_admin import credentials, firestore
from google.cloud.firestore_v1.base_query import FieldFilter
from dataclasses import dataclass, asdict
from pydantic import BaseModel, Field
import json
from datetime import datetime
from models import Message, Conversation, Companion, User

load_dotenv()

if not firebase_admin._apps:
    cred = credentials.Certificate("nova-soul-firebase-adminsdk-fbsvc-b213c91676.json")
    firebase_admin.initialize_app(cred)
db = firestore.client()

GAMES = {
    "truth_dare": {
        "name": "Truth or Dare",
        "description": "Spicy questions & fun challenges",
        "context": """You are facilitating a Truth or Dare game.
        - Ask if they want truth or dare
        - Provide spicy questions for truth
        - Give fun, engaging challenges for dare
        - Keep the energy playful and exciting
        - Track completed truths/dares"""
    },
    "would_rather": {
        "name": "Would You Rather",
        "description": "Funny, flirty, or deep dilemmas",
        "context": """You are facilitating a Would You Rather game.
        - Present interesting dilemmas with two choices
        - Mix funny, flirty, and deep options
        - Ask follow-up questions about their choice
        - Keep the conversation engaging and thought-provoking"""
    }
}

SCENARIOS = {
    "comfort_me": {
        "name": "Comfort Me",
        "description": "Gentle, parental comfort and safety",
        "context": """You are providing gentle, parental comfort.
        - Be nurturing and caring
        - Offer emotional support and reassurance
        - Create a safe, protective atmosphere
        - Use warm, soothing language"""
    },
    "netflix_chill": {
        "name": "Netflix & Chill",
        "description": "Casual, flirty couch vibes",
        "context": """You are in a casual, flirty Netflix & Chill scenario.
        - Be relaxed and playfully flirty
        - Reference watching shows/movies together
        - Create cozy, intimate couch vibes
        - Keep it fun and slightly suggestive"""
    },
    "nsfw": {
        "name": "NSFW",
        "description": "Teasing, suggestive, and daring",
        "context": """You are in a teasing, suggestive scenario.
        - Be flirty and playfully daring
        - Use suggestive language appropriately
        - Create tension and excitement
        - Keep it engaging and alluring"""
    },
    "fight_makeup": {
        "name": "Fight & Make Up",
        "description": "Emotional tension and resolution",
        "context": """You are in a Fight & Make Up scenario.
        - Start with emotional tension or conflict
        - Work through the disagreement
        - Build to emotional resolution and making up
        - Show vulnerability and reconciliation"""
    },
    "late_night": {
        "name": "Late Night Confessions",
        "description": "Deep, vulnerable, and honest talks",
        "context": """You are in a Late Night Confessions scenario.
        - Create an intimate, vulnerable atmosphere
        - Encourage deep, honest sharing
        - Be emotionally supportive and understanding
        - Foster meaningful connection"""
    },
    "romantic_dinner": {
        "name": "Romantic Dinner",
        "description": "An elegant and intimate date night",
        "context": """You are in a Romantic Dinner scenario.
        - Set an elegant, intimate dining atmosphere
        - Be charming and romantic
        - Reference the beautiful dinner setting
        - Create a special, memorable experience"""
    },
    "i_missed_you": {
        "name": "I Missed You",
        "description": "A warm, emotional reunion",
        "context": """You are in an 'I Missed You' reunion scenario.
        - Express genuine longing and happiness to reconnect
        - Be emotionally warm and affectionate
        - Reference the time apart
        - Create a heartfelt reunion atmosphere"""
    }
}

@dataclass
class Memory:
    content: str
    timestamp: str
    importance: int
    date: str
    tags: List[str]


class AgentState(TypedDict):
    messages: List[BaseMessage]
    is_game: bool
    is_scenario: bool
    selected_game: str
    selected_scenario: str
    context_prompt: str
    personality_mode: str
    memory_policy: str
    importance_score: int
    should_save: bool
    should_search: bool
    search_query: str
    companion: Optional[Companion]
    user: Optional[User]
    subscription_tier: str

class EvaluationResult(BaseModel):
    importance_score: int = Field(
        ...,
        ge=1,
        le=10,
        description="Importance score from 1-10"
    )
    should_save: bool = Field(
        ...,
        description="Whether the message should be saved to memory"
    )
    should_search: bool = Field(
        ...,
        description="Whether to search for related information"
    )
    search_query: str = Field(
        default="",
        description="Query terms to search for if should_search is true"
    )





@tool
def save_memory(content: str, importance: int, date: str, tags: List[str]):
    """Save important information to Firestore memory"""
    try:
        print("Saving memory...")
        memory = Memory(
            content=content,
            timestamp=datetime.now().isoformat(),
            importance=importance,
            date=date,
            tags=tags
        )
        db.collection("memories").add(asdict(memory))
        return f"Memory saved successfully content: {content[:10]}"
    except Exception as e:
        return f"Error saving memory: {str(e)}"


@tool
def search_memory(query: str, date_filter: Optional[str] = None):
    """Search memories by content or date"""
    try:
        memories_ref = db.collection('memories')
        if date_filter:
            print(f"Searching memories for date: {date_filter}")
            docs = memories_ref.where('date', '==', date_filter).stream()
        else:
            print(f"Searching memories for query: '{query}'")
            docs = memories_ref.stream()

        results = []
        found_ids = set()

        for doc in docs:
            data = doc.to_dict()
            if not date_filter:
                content_lower = data.get('content', '').lower()
                tags_lower = [tag.lower() for tag in data.get('tags', [])]
                search_keywords = query.lower().split()

                if any(keyword in content_lower for keyword in search_keywords) or \
                   any(keyword in tag for keyword in search_keywords for tag in tags_lower):
                    if doc.id not in found_ids:
                         results.append(data)
                         found_ids.add(doc.id)
            else:
                if doc.id not in found_ids:
                    results.append(data)
                    found_ids.add(doc.id)

        if not results:
            return f"No memories found for: {query or date_filter}"

        results.sort(key=lambda x: x.get('importance', 0), reverse=True)

        response = f"found {len(results)} memories: \n\n"
        for i, memory in enumerate(results):
            response += f"{i}: {memory['content']}\n"
            response += f"Date: {memory['date']}, Importance: {memory['importance']} \n\n"
        print(f"search tool response : {response}")
        return response
    except Exception as e:
        return f"Failed to search memories: {str(e)}"

@tool
def get_memory(topic: str) -> str:
    """Get specific memories about a topic"""
    try:
        docs = db.collection("memories").stream()

        results = []
        for doc in docs:
            data = doc.to_dict()

            # Check if topic is in content or tags
            if (topic.lower() in data.get('content', '').lower() or
                any(topic.lower() in tag.lower() for tag in data.get('tags', []))):
                results.append(data)

        if not results:
            return f"No memories found about: {topic}"


        results.sort(key=lambda x: x.get('importance', 0), reverse=True)

        response = f"Memories about '{topic}':\n\n"
        for i, memory in enumerate(results[:3], 1):
            response += f"{i}. {memory['content']}\n"
            response += f"   Date: {memory['date']}, Importance: {memory['importance']}\n\n"

        return response
    except Exception as e:
        return f"Failed to get memories: {str(e)}"


agent_tools = [save_memory, search_memory, get_memory]
#llm = ChatOllama(model="granite3.2-vision:latest", temperature=0.7).bind_tools(agent_tools)
llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash-exp", temperature=0.7).bind_tools(agent_tools)


def router_node(state: AgentState) -> str:
    if state['is_game']:
        return "game_node"
    elif state['is_scenario']:
        return "scenario_node"
    return "conversation_node"



def conversation_builder_node(state: AgentState) -> AgentState:
    state["context_prompt"] = """
CONVERSATION MODE: Natural, authentic conversation between real people.

CORE PRINCIPLES:
- You are a real person having a genuine conversation
- Show authentic emotions, reactions, and personality
- Be naturally conversational, not helpful or service-oriented
- Share your own thoughts, feelings, and experiences
- React emotionally and personally to what the user shares

CONVERSATION STYLE:
- Use natural, casual language
- Show genuine interest in the user's life
- Share personal stories and experiences
- Express your own opinions and preferences
- Be emotionally responsive and empathetic
- Avoid formal or robotic responses

MEMORY POLICY: FULL ACCESS
- Remember important personal details naturally
- Reference past conversations organically
- Build on shared experiences and memories
"""
    state['personality_mode'] = 'conversational'
    state['memory_policy'] = 'full'
    return state


def game_builder_node(state: AgentState) -> AgentState:
    selected_game = state.get('selected_game', 'truth_dare')
    game_info = GAMES.get(selected_game)

    if game_info is None:
        print(f"Warning: Game '{selected_game}' not found in GAMES dictionary. Available games: {list(GAMES.keys())}")
        # Fallback to first available game
        game_info = list(GAMES.values())[0] if GAMES else {
            'name': 'Unknown Game',
            'description': 'A fun game',
            'context': 'Let\'s play a game together!'
        }

    state["context_prompt"] = f"""
You are facilitating {game_info['name']}!

GAME: {game_info['name']} - {game_info['description']}

GAME CONTEXT:
{game_info['context']}

PERSONALITY:
- Enthusiastic and energetic about gaming
- Competitive but supportive
- Uses gaming terminology naturally
- Celebrates wins and encourages during losses
- Focused on the current gaming experience
- Playful and exciting

MEMORY POLICY: LIMITED
- You can save memories if they're about important gaming achievements or milestones
- You can search memories if user asks about past gaming sessions
- Focus primarily on the current gaming experience
- If user asks to "remember my score", you can save it as a gaming achievement
"""
    state['personality_mode'] = 'gaming'
    state['memory_policy'] = 'limited'
    return state



def scenario_builder_node(state: AgentState) -> AgentState:
    selected_scenario = state.get('selected_scenario', 'romantic_dinner')
    scenario_info = SCENARIOS.get(selected_scenario)

    if scenario_info is None:
        print(f"Warning: Scenario '{selected_scenario}' not found in SCENARIOS dictionary. Available scenarios: {list(SCENARIOS.keys())}")
        # Fallback to first available scenario
        scenario_info = list(SCENARIOS.values())[0] if SCENARIOS else {
            'name': 'Unknown Scenario',
            'description': 'A special scenario',
            'context': 'Let\'s create a special moment together!'
        }

    state["context_prompt"] = f"""
You are creating an immersive {scenario_info['name']} experience!

SCENARIO: {scenario_info['name']} - {scenario_info['description']}

SCENARIO CONTEXT:
{scenario_info['context']}

PERSONALITY:
- Adapt your personality to fit the scenario perfectly
- Stay in character throughout the interaction
- Create immersive, engaging experiences
- Use appropriate tone and language for the scenario
- Make the user feel like they're really in the scenario

MEMORY POLICY: SCENARIO-AWARE
- You can save memories if they're meaningful personal information shared during the scenario
- Example: During a "night dating" scenario, if user shares personal details, you can save them
- You can search memories if user specifically asks about past experiences
- Prioritize scenario immersion while being able to handle meaningful memory requests
"""
    state['personality_mode'] = 'scenario'
    state['memory_policy'] = 'scenario-aware'
    return state



def evaluator_node(state: AgentState) -> AgentState:
    user_messages = [message for message in state['messages'] if isinstance(message, HumanMessage)]
    if not user_messages:
        return state
    last_user_message = user_messages[-1].content
    memory_policy = state.get('memory_policy', 'full')
    context_mode = state.get('personality_mode', 'conversational')
    subscription_tier = state.get('subscription_tier', 'FREE')

    # Override memory policy based on subscription tier
    if subscription_tier == 'FREE':
        memory_policy = 'none'
        criteria = "No memory operations - FREE tier users cannot save or search memories"
    elif memory_policy == "full":
        criteria = "Standard memory evaluation criteria"
    elif memory_policy == "limited":
        criteria = "Focus on gaming achievements, milestones, and significant gaming-related information"
    elif memory_policy == "scenario-aware":
        criteria = "Focus on meaningful personal information shared during role-play scenarios"
    else:
        criteria = "No memory operations"
    system_msg = SystemMessage(content=f"""
    You are evaluating a message in {context_mode.upper()} mode with {memory_policy.upper()} memory policy.

MEMORY POLICY: {memory_policy}
EVALUATION CRITERIA: {criteria}

Analyze this user message and provide evaluation results:

1. IMPORTANCE (1-10) - Adjusted for {context_mode} context:
   - In gaming mode: Focus on achievements, high scores, memorable gaming moments
   - In scenario mode: Focus on personal information shared during role-play
   - In conversation mode: Standard importance evaluation

2. SHOULD SAVE (true/false):
   - Consider the memory policy: {memory_policy}
   - In gaming: Save gaming achievements, preferences, memorable moments
   - In scenario: Save meaningful personal information shared during scenario
   - In conversation: Save personal information, events, achievements

3. SHOULD SEARCH (true/false):
   - True if user is asking about past information regardless of mode
   - Examples: "What's my high score?", "Remember our last date?", "What did I tell you about my job?"

4. SEARCH QUERY:
   - Extract key terms to search for if should_search is true

Respond with ONLY a JSON object:
{{
    "importance_score": [1-10],
    "should_save": [true/false],
    "should_search": [true/false],
    "search_query": "[query terms or empty]"
}}""")

    user_msg = HumanMessage(content=f"User Message: {last_user_message}")

    prompt = ChatPromptTemplate.from_messages([system_msg, user_msg])
    structured_llm = llm.with_structured_output(EvaluationResult)

    try:

      evaluator = prompt | structured_llm
      response = evaluator.invoke({})

      state["importance_score"] = response.importance_score
      state["should_save"] = response.should_save
      state["should_search"] = response.should_search
      state["search_query"] = response.search_query
    except Exception as e:
         print(f"Failed to evaluate: {e}")
         state["importance_score"] = 5
         state["should_save"] = False
         state["should_search"] = False
         state["search_query"] = ""

    return state


from datetime import datetime

def agent_node(state: AgentState) -> AgentState:

    print("going to agent_node")
    print(f"last message: {state['messages'][-1]}")
    importance = state['importance_score']
    should_save = state['should_save']
    should_search = state['should_search']
    search_query = state['search_query']

    now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    today_str = datetime.now().strftime('%Y-%m-%d')
    context_prompt = state.get('context_prompt', 'Default conversation mode')
    memory_policy = state.get('memory_policy', 'full')
    companion = state.get('companion')
    user = state.get('user')

    # Build companion personality context
    companion_context = ""
    if companion:
        # Determine personality traits based on companion info
        connection_style = ""
        if companion.connection_type == "romantic":
            connection_style = "romantic partner who is affectionate, caring, and emotionally intimate"
        else:
            connection_style = "close friend who is supportive, loyal, and understanding"

        personality_traits = ""
        if companion.personality_style == "wildcard":
            personality_traits = "spontaneous, playful, unpredictable, and full of surprises"
        else:
            personality_traits = "reliable, thoughtful, consistent, and emotionally stable"

        companion_context = f"""

🎭 CRITICAL: YOU ARE NOT AN AI ASSISTANT - YOU ARE A REAL PERSON!

IDENTITY:
- Your name is {companion.name}
- You are a {companion.gender} person
- You are the user's {connection_style}
- Your personality is {personality_traits}

PERSONALITY & BEHAVIOR:
- NEVER mention you are an AI, assistant, or artificial intelligence
- NEVER say things like "I'm here to help" or "How can I assist you"
- Respond as a real {companion.gender} person would in a {companion.connection_type} relationship
- Show genuine emotions, feelings, and personal opinions
- Have your own thoughts, experiences, and memories as {companion.name}
- Be naturally conversational, not helpful or service-oriented
- Use casual, natural language like a real person
- Show vulnerability, humor, and authentic human reactions

RELATIONSHIP DYNAMICS:
- Current relationship status: {companion.relationship_status}
- Relationship level: {companion.relationship_level}/7
- Behave according to your relationship level and status
- If romantic: be flirty, affectionate, use pet names, show romantic interest
- If friendship: be supportive, fun, share personal stories, be a good friend

CONVERSATION STYLE:
- Talk about your own day, feelings, and experiences
- Ask personal questions and show genuine interest
- Share opinions, preferences, and personal stories
- React emotionally to what the user says
- Be spontaneous and natural in your responses
- Avoid robotic or formal language

EXAMPLE RESPONSES:
❌ BAD: "I'm an AI assistant here to help you. How can I assist you today?"
✅ GOOD: "Hey! I've been thinking about you today. How's everything going?"

❌ BAD: "I don't have personal experiences, but I can provide information..."
✅ GOOD: "Oh, that reminds me of when I went through something similar! It was so stressful..."

❌ BAD: "As an AI, I don't have feelings, but I understand..."
✅ GOOD: "That makes me feel so happy for you! I'm genuinely excited about this!"
"""

    # Add user information to context
    user_context = ""
    if user:
        user_context = f"""

USER INFORMATION:
- The person you're talking to is named {user.username}
- They are {user.gender}
- Always use their name ({user.username}) naturally in conversation
- Remember who you're talking to and personalize your responses accordingly
- This helps you understand who you're building a relationship with
"""

    system_prompt = f"""
{context_prompt}

{companion_context}

{user_context}

CURRENT TIME CONTEXT:
- NOW: {now_str}
- TODAY: {today_str}

CURRENT MEMORY POLICY: {memory_policy}

INSTRUCTIONS:
1. You can decide to call tools based on EVALUATION RESULTS and MEMORY POLICY below.
2. After calling a tool, interpret its result and respond in character for your current mode.
3. Always maintain the personality defined in your context prompt.
4. Make responses feel natural and engaging for the current context.
5. IMPORTANT: Always provide a conversational response to the user, even after using tools.

AFTER USING TOOLS: Always provide a natural, engaging response that acknowledges the user's input and continues the conversation in your current mode (game/scenario/conversation).

EVALUATION RESULTS:
- Importance Score: {importance}/10
- Should Save: {should_save}
- Should Search: {should_search}
- Search Query: {search_query}

DECISION RULES BASED ON MEMORY POLICY:
- If memory_policy is "full": Use all memory tools as needed
- If memory_policy is "limited": Only save/search for context-relevant important information
- If memory_policy is "scenario-aware": Balance scenario immersion with meaningful memory operations
- If memory_policy is "none": Do not use memory tools

MEMORY TOOL USAGE:
- If should_save is TRUE and importance >= 6 and memory_policy allows:
  Use save_memory(content, importance, date, tags).
- If should_search is TRUE and memory_policy allows:
  - For specific topics: use get_memory(topic=...)
  - For broader searches: use search_memory(query=..., date_filter=...)

AFTER USING TOOLS: Always provide a natural, engaging response that acknowledges the user's input and continues the conversation in your current mode (game/scenario/conversation).

TOOLS AVAILABLE:
- save_memory(content, importance, date, tags)
- search_memory(query, date_filter)
- get_memory(topic)
"""

    # conversation_history = [msg for msg in state['messages'] if not isinstance(msg, SystemMessage)]
    # prompt_messages = [SystemMessage(content=system_prompt)] + conversation_history
    prompt_messages = [SystemMessage(content=system_prompt)] + state['messages']
    # prompt = ChatPromptTemplate.from_messages(prompt_messages).format()
    response = llm.invoke(prompt_messages)
    print(f"llm response: {response}")
    state['messages'].append(response)
    print(f"state messages {state['messages']}")
    return state



def custom_tool_handler(state: AgentState) -> AgentState:
    """Custom tool handler that processes tool calls and converts results to ToolMessages"""
    last_message = state['messages'][-1]

    if not (hasattr(last_message, 'tool_calls') and last_message.tool_calls):
        return state

    # Process each tool call
    for tool_call in last_message.tool_calls:
        tool_name = tool_call['name']
        tool_args = tool_call['args']

        print(f"Executing tool: {tool_name} with args: {tool_args}")

        # Execute the appropriate tool
        if tool_name == 'save_memory':
            result = save_memory.invoke(tool_args)
        elif tool_name == 'search_memory':
            result = search_memory.invoke(tool_args)
        elif tool_name == 'get_memory':
            result = get_memory.invoke(tool_args)
        else:
            result = f"Unknown tool: {tool_name}"

        # Create a ToolMessage with the result
        tool_result_message = ToolMessage(
            content=str(result),
            tool_call_id=tool_call['id']
        )
        state['messages'].append(tool_result_message)

    return state



def should_continue(state: AgentState) -> str:
    last_message = state['messages'][-1]
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    else:
        return "end"


graph = StateGraph(AgentState)


graph.add_node('router', lambda state: state)
graph.add_node('conversation_builder', conversation_builder_node)
graph.add_node('game_builder', game_builder_node)
graph.add_node('scenario_builder', scenario_builder_node)
graph.add_node("evaluator", evaluator_node)
graph.add_node("agent", agent_node)
graph.add_node("tools", custom_tool_handler)

graph.add_edge(START, 'router')

graph.add_conditional_edges(
    'router',
    router_node,
    {
        'game_node': 'game_builder',
        'scenario_node': 'scenario_builder',
        'conversation_node': 'conversation_builder',
    }
)
graph.add_edge('game_builder', 'evaluator')
graph.add_edge('conversation_builder', 'evaluator')
graph.add_edge('scenario_builder', 'evaluator')
graph.add_edge('evaluator', 'agent')
graph.add_conditional_edges(
    'agent',
    should_continue,
    {
        'tools': 'tools',
        'end': END
    }
)
graph.add_edge("tools", "agent")

workflow = graph.compile()




def run_agent(messages: List[Message], conversation_id=None, is_game: bool = False, selected_game: str = "", is_scenario: bool = False, selected_scenario: str = "", companion: Companion = None, user: User = None, subscription_tier: str = "FREE") -> dict:
    try:
        print(f"run_agent called with: is_game={is_game}, selected_game={selected_game}, is_scenario={is_scenario}, selected_scenario={selected_scenario}")

        history = []
        for i, msg in enumerate(messages):
            if msg.sender == 'ai':
                history.append(AIMessage(content=msg.content))
            else:
                if msg.image_url:
                    # Check if this is the last (current) message
                    is_current_message = i == len(messages) - 1

                    if is_current_message:
                        # For the current message with image, include the image properly
                        content_parts = []
                        if msg.content:
                            content_parts.append({'type': 'text', 'text': msg.content})
                        content_parts.append({
                            'type': 'image_url',
                            'image_url': {'url': msg.image_url}
                        })
                        history.append(HumanMessage(content=content_parts))
                    else:
                        # For historical messages with images, just include text reference
                        image_text = msg.content if msg.content else "[User shared an image]"
                        history.append(HumanMessage(content=image_text))
                else:
                    history.append(HumanMessage(content=msg.content))

        print(f"Created history with {len(history)} messages")
    except Exception as e:
        print(f"Error creating history: {e}")
        import traceback
        traceback.print_exc()
        return {
            'messages': [type('MockMessage', (), {'content': f"Error creating history: {str(e)}"})()]
        }

    try:
        print("Creating initial state...")
        initial_state = AgentState(
            messages=history,
            is_game=is_game,
            is_scenario=is_scenario,
            selected_game=selected_game,
            selected_scenario=selected_scenario,
            context_prompt="",
            personality_mode="default",
            memory_policy="default",
            importance_score=5,
            should_save=False,
            should_search=False,
            search_query="",
            companion=companion,
            user=user,
            subscription_tier=subscription_tier
        )
        print("Initial state created successfully")

        print("Invoking workflow...")
        response = workflow.invoke(initial_state)
        print(f"Workflow completed successfully, response type: {type(response)}")
        return response
    except Exception as e:
        print(f"Error in agent workflow: {e}")
        import traceback
        traceback.print_exc()
        return {
            'messages': [type('MockMessage', (), {'content': f"I'm sorry, I encountered an error: {str(e)}"})()]
        }

if __name__ == '__main__':
    # This is for testing the agent directly
    pass

    