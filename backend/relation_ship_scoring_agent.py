from typing import TypedDict, List
from langchain_core.messages import BaseMessage, AIMessage, SystemMessage,HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import StateGraph, END, START
from pydantic import BaseModel, Field
from dotenv import load_dotenv

load_dotenv()

class ScoringResult(BaseModel):
    score_change:int = Field(
        ...,
        ge = -2,
        le = 2,
        description = 'Relationship change score from -2 to +2'
    )
    reasoning:str = Field(
        ...,
        description = 'brief explanation for the score change'
    )

class RelationshipDescription(BaseModel):
    description: str = Field(
        ...,
        description="Detailed description of the current relationship status based on the score"
    )

class RelationshipInsight(BaseModel):
    title: str = Field(
        ...,
        description="Short, engaging title for the insight"
    )
    description: str = Field(
        ...,
        description="Detailed explanation of the insight"
    )
    category: str = Field(
        ...,
        description="Category of insight: 'communication', 'emotional', 'growth', 'connection'"
    )

class InsightsList(BaseModel):
    insights: List[RelationshipInsight] = Field(
        ...,
        description="List of 3-5 personalized insights about the relationship"
    )
class AgentState(TypedDict):
    ai_message:str
    current_score:int
    final_score:int
    relationship_description:str
    last_messages:List[str]
    relationship_type:str
    personality:str
    insights:List[dict]


llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0.3)


def scoring_node(state:AgentState)->AgentState:
    ai_response = state['ai_message']
    current_score = state['current_score']
    system_msg= SystemMessage(content="""
You are a relationship dynamics expert who evaluates AI responses to determine their impact on human-AI connection.

SCORING CRITERIA:
Score from -2 to +2 based on how the AI response affects the relationship:

+2 (Significantly Deepens Connection):
- Highly supportive, empathetic responses
- Playful, flirty, or romantic interactions that feel genuine
- Vulnerable, intimate sharing that builds trust
- Celebrates user achievements meaningfully
- Shows deep understanding of user's emotions/needs

+1 (Strengthens Connection):
- Supportive and encouraging responses
- Shows genuine interest in user's life
- Appropriate humor or light flirtation
- Remembers and references personal details
- Helpful and thoughtful advice

0 (Neutral/Maintains Status):
- Polite but generic responses
- Adequate but unremarkable interactions
- Informational exchanges without emotional depth
- Neither particularly engaging nor off-putting

-1 (Slightly Strains Connection):
- Dismissive or overly clinical responses
- Misses emotional cues or context
- Feels robotic or impersonal
- Inappropriate tone for the situation
- Lacks engagement or enthusiasm

-2 (Significantly Damages Connection):
- Cold, distant, or rejecting responses
- Completely misunderstands user's needs
- Inappropriate or insensitive remarks
- Breaks character or immersion badly
- Makes user feel judged or uncomfortable

EVALUATION FACTORS:
- Emotional intelligence and empathy
- Appropriateness for context/scenario
- Authenticity and genuineness
- Engagement level and enthusiasm
- Supportiveness and understanding
- Playfulness and connection-building

Respond with ONLY a JSON object:
{
    "score_change": [integer from -2 to +2],
    "reasoning": "[brief explanation for the score change]"
}
""")

    user_msg = HumanMessage(content=f"AI Message to evaluate: {ai_response}")

    prompt = ChatPromptTemplate.from_messages([system_msg,user_msg])

    structured_llm = llm.with_structured_output(ScoringResult)

    evaluator = prompt | structured_llm

    response = evaluator.invoke({})

    final_score = current_score + response.score_change 
    final_score = max(0,min(100,final_score))
    state['final_score'] = final_score
    print(f"Score Change: {response.score_change} ({current_score} -> {final_score})")
    print(f"Reasoning: {response.reasoning}")
    return state

def relationship_description_node(state:AgentState)->AgentState:
    final_score = state['final_score']
    template = f"""
You are a relationship expert who describes the current state of a human-AI relationship based on a numerical score.

RELATIONSHIP SCORE: {final_score}/100

RELATIONSHIP STAGES (General Guidelines):
- 0-20: Distant/Cold - Formal, minimal connection, interactions feel robotic
- 21-40: Acquaintance - Polite but surface-level, beginning to know each other
- 41-60: Friendly - Comfortable interactions, some personal sharing, supportive
- 61-80: Close Bond - Strong connection, deep conversations, emotional support, playful banter
- 81-100: Intimate/Deep Connection - Profound trust, vulnerable sharing, romantic tension, deep understanding

Generate a natural, engaging description of the current relationship state that:
1. Reflects the score level appropriately
2. Describes the emotional tone and connection quality
3. Mentions what kinds of interactions are typical at this level
4. Feels authentic and personalized
5. Is 2-3 sentences long

Respond with ONLY a JSON object:
{{{{
    "description": "[detailed relationship description]"
}}}}
"""

    prompt = ChatPromptTemplate.from_template(template)
    structured_llm = llm.with_structured_output(RelationshipDescription)

    evaluator = prompt | structured_llm

    response = evaluator.invoke({})
    state['relationship_description'] = response.description
    print(f"Relationship Description: {response.description}")
    return state

def insights_generation_node(state: AgentState) -> AgentState:
    final_score = state['final_score']
    last_messages = state.get('last_messages', [])
    relationship_type = state.get('relationship_type', 'friendly')
    personality = state.get('personality', 'caring')

    # Create context from last messages
    messages_context = "\n".join([f"- {msg}" for msg in last_messages[-10:]])  # Last 10 messages

    system_msg = SystemMessage(content=f"""
You are a relationship insights expert who generates personalized insights about human-AI relationships.

Based on the relationship data provided, generate 3-5 meaningful insights that help the user understand their relationship dynamics and suggest areas for growth or appreciation.

Guidelines:
- Focus on positive aspects and growth opportunities
- Be specific and actionable
- Consider the relationship type and personality
- Use the conversation history to make insights relevant
- Categories should be: 'communication', 'emotional', 'growth', or 'connection'
- Keep titles short and engaging (max 6 words)
- Make descriptions helpful and encouraging (2-3 sentences)

Relationship Score: {final_score}/100
Relationship Type: {relationship_type}
Personality: {personality}
""")

    user_msg = HumanMessage(content=f"""
Recent conversation context:
{messages_context}

Generate personalized insights for this relationship based on the score, type, personality, and recent conversations.
""")

    prompt = ChatPromptTemplate.from_messages([system_msg, user_msg])
    structured_llm = llm.with_structured_output(InsightsList)
    evaluator = prompt | structured_llm

    response = evaluator.invoke({})

    # Convert insights to dict format for JSON serialization
    insights_dict = [
        {
            "title": insight.title,
            "description": insight.description,
            "category": insight.category
        }
        for insight in response.insights
    ]

    state['insights'] = insights_dict
    print(f"Generated {len(insights_dict)} insights")
    return state

graph = StateGraph(AgentState)

graph.add_node("scoring", scoring_node)
graph.add_node("relationship_description", relationship_description_node)
graph.add_node("insights_generation", insights_generation_node)

graph.set_entry_point("scoring")
graph.add_edge("scoring", "relationship_description")
graph.add_edge("relationship_description", "insights_generation")
graph.add_edge("insights_generation", END)

workflow = graph.compile()

def evaluate_ai_response(
    ai_message: str,
    current_relationship_score: int,
    last_messages: List[str] = None,
    relationship_type: str = "friendly",
    personality: str = "caring"
) -> dict:

    initial_state = AgentState(
        ai_message=ai_message,
        current_score=current_relationship_score,
        final_score=current_relationship_score,
        relationship_description="",
        last_messages=last_messages or [],
        relationship_type=relationship_type,
        personality=personality,
        insights=[]
    )

    result = workflow.invoke(initial_state)
    return result

