# Demo Account Setup Guide for App Store Review

This guide provides step-by-step instructions for creating and configuring a demo account to resolve Apple App Store Guideline 2.1 - Information Needed.

## Overview

Your app currently requires users to complete a mandatory profile setup (username and gender) after signing in, which blocks Apple reviewers from accessing the app's full functionality. This guide provides two solutions:

1. **Demo Google Account** (Recommended)
2. **Demo Mode Implementation** (Alternative)

---

## Solution 1: Demo Google Account (Recommended)

### Step 1: Create Demo Google Account

1. Go to [accounts.google.com](https://accounts.google.com)
2. Click "Create account" → "For personal use"
3. Fill in the details:
   ```
   First name: Nova
   Last name: Soul Demo
   Username: <EMAIL> (or similar available)
   Password: AppReview2024!
   ```
4. Complete the verification process
5. **Important**: Save these credentials securely

### Step 2: Pre-configure Demo Account in Firebase

1. **Sign in to your app** using the demo Google account
2. **Complete the profile setup** with demo data:
   ```
   Username: DemoUser
   Gender: Male (or any preference)
   Profile Image: Optional
   ```
3. **Create a demo companion** with sample data:
   ```
   Name: Alex
   Gender: Female
   Connection Type: Romantic
   Personality: Caring
   ```
4. **Add sample conversation history** (optional but recommended)

### Step 3: Provide Credentials to Apple

In your App Store Connect submission, add this to the "Review Notes":

```
Demo Account Credentials:
Email: <EMAIL>
Password: AppReview2024!

Instructions:
1. Tap "Continue with Google" on the sign-in screen
2. Sign in with the provided credentials
3. The account is pre-configured with sample data and bypasses onboarding
4. All app features are immediately accessible

Note: This is a dedicated demo account created specifically for App Store review purposes.
```

---

## Solution 2: Demo Mode Implementation (Alternative)

If you prefer not to create a Google account, you can implement a hidden demo mode:

### Implementation Steps

1. **Add a hidden demo button** to the sign-in screen (tap logo 5 times)
2. **Create a demo user** that bypasses authentication
3. **Pre-populate with sample data**

### Code Implementation

Add this to your `SignInPage`:

```dart
// Add this to your SignInPage widget
int _logoTapCount = 0;

// In the logo widget, wrap with GestureDetector:
GestureDetector(
  onTap: () {
    _logoTapCount++;
    if (_logoTapCount >= 5) {
      // Trigger demo mode
      context.read<AuthBloc>().add(AuthDemoModeRequested());
      _logoTapCount = 0;
    }
  },
  child: Container(
    // Your existing logo widget
  ),
)
```

### Instructions for Apple

```
Demo Mode Access:
1. On the sign-in screen, tap the app logo 5 times quickly
2. This will activate demo mode and bypass all authentication
3. The app will load with pre-configured sample data
4. All features will be immediately accessible
```

---

## Recommended Approach

**Use Solution 1 (Demo Google Account)** because:

✅ **Professional**: Uses the actual authentication flow  
✅ **Realistic**: Shows real user experience  
✅ **Secure**: Dedicated account for reviews only  
✅ **Reusable**: Same account for future app updates  
✅ **Compliant**: Meets Apple's requirements exactly  

---

## Firebase Configuration Requirements

To enable Apple Sign In in Firebase Console:

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: `nova-soul`
3. Go to **Authentication** → **Sign-in method**
4. Enable **Apple** provider
5. Configure with your iOS Bundle ID: `com.novasoul.app`

---

## iOS Project Configuration

The following configurations are already completed in your project:

✅ **Sign in with Apple capability** (will be enabled in Xcode)  
✅ **Package dependency** added to `pubspec.yaml`  
✅ **Firebase integration** updated  
✅ **UI components** created  

### Manual Steps Required

1. **Open Xcode**: `open ios/Runner.xcworkspace`
2. **Select Runner target** → **Signing & Capabilities**
3. **Add Capability**: Click "+" and add "Sign in with Apple"
4. **Build and test** on a physical iOS device (Apple Sign In requires real device)

---

## Testing Checklist

Before submitting to App Store:

- [ ] Demo Google account created and configured
- [ ] Profile setup completed for demo account
- [ ] Sample companion created
- [ ] Apple Sign In capability enabled in Xcode
- [ ] Both Google and Apple sign-in buttons visible on iOS
- [ ] App tested on physical iOS device
- [ ] Demo credentials added to App Store Connect review notes

---

## App Store Submission Notes

Add this exact text to your App Store Connect "Review Notes":

```
DEMO ACCOUNT INFORMATION:

Email: [your-demo-email]@gmail.com
Password: [your-demo-password]

INSTRUCTIONS:
1. Tap "Continue with Google" 
2. Sign in with provided credentials
3. Account is pre-configured - no setup required
4. All features immediately accessible

APPLE SIGN IN:
- Sign in with Apple is now available as alternative login
- Complies with Guideline 4.8 requirements
- Provides equivalent functionality to Google Sign In

This resolves both Guideline 2.1 (Information Needed) and Guideline 4.8 (Login Services).
```

---

## Support

If you encounter any issues:

1. **Authentication problems**: Check Firebase configuration
2. **Apple Sign In issues**: Ensure capability is enabled in Xcode
3. **Demo account issues**: Verify account is properly configured in Firebase

The implementation is now complete and ready for App Store submission!
