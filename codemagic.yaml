workflows:
  flutter-ios-workflow:
    name: Nova Soul iOS Workflow
    max_build_duration: 120
    instance_type: mac_mini_m2
    integrations:
      app_store_connect: "NovaSoul CM Key"
    environment:
      groups:
        - nova-soul-mobile-app
      ios_signing:
        distribution_type: app_store
        bundle_identifier: com.novasoul.app
      vars:
        PACKAGE_NAME: "com.novasoul.app"
        CM_CLONE_DEPTH: 10
        JAVA_TOOL_OPTIONS: "-Xmx4096m"
      flutter: stable
      cocoapods: default
    triggering:
      events:
        - tag
        - push
      tag_patterns:
        - pattern: "v*.*.*"
          include: true
      branch_patterns:
        - pattern: "*"
          include: true
    scripts:
      - name: Set up automatic versioning from GitHub tag
        script: |
          echo "📋 GitHub tag: $CM_TAG"
          echo "📋 Build number: $PROJECT_BUILD_NUMBER"

          # Handle versioning for both tagged and manual builds
          if [[ -z "$CM_TAG" ]]; then
            # Manual build without tag - use default version
            export BUILD_NAME="1.0.0"
            echo "BUILD_NAME=$BUILD_NAME" >> $CM_ENV
            echo "🔧 Manual build detected, using version: $BUILD_NAME"
          elif [[ "$CM_TAG" =~ ^v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
            # Extract version from GitHub tag for build name (e.g., v1.2.3 -> 1.2.3)
            export BUILD_NAME="${BASH_REMATCH[1]}"
            echo "BUILD_NAME=$BUILD_NAME" >> $CM_ENV
            echo "✅ Extracted version: $BUILD_NAME from tag: $CM_TAG"
          else
            # Fallback to a default version if tag format is unexpected
            export BUILD_NAME="1.0.0"
            echo "BUILD_NAME=$BUILD_NAME" >> $CM_ENV
            echo "⚠️  Unexpected tag format, using fallback version: $BUILD_NAME"
          fi

          # Use Codemagic's automatic build number incrementing
          export BUILD_NUMBER=$PROJECT_BUILD_NUMBER
          echo "BUILD_NUMBER=$BUILD_NUMBER" >> $CM_ENV

          echo "🚀 Final build version: $BUILD_NAME+$BUILD_NUMBER"

      - name: Generate release notes from GitHub release
        script: |
          if command -v curl &> /dev/null; then
            GITHUB_RELEASE_URL="https://api.github.com/repos/${CM_REPO_SLUG}/releases/tags/${CM_TAG}"
            echo "Fetching release notes from: $GITHUB_RELEASE_URL"
            RELEASE_BODY=$(curl -s -H "Accept: application/vnd.github.v3+json" "$GITHUB_RELEASE_URL" | grep -o '"body":"[^"]*"' | sed 's/"body":"//;s/"$//' | sed 's/\\n/\n/g' | sed 's/\\r//g')
            if [ -n "$RELEASE_BODY" ] && [ "$RELEASE_BODY" != "null" ]; then
              echo "Using GitHub release notes"
              echo "$RELEASE_BODY" > release_notes.txt
            else
              echo "No GitHub release found, generating from git commits"
              git fetch --all --tags
              prev_tag=$(git for-each-ref --sort=-creatordate --format '%(refname:short)' refs/tags | grep -v "^$CM_TAG$" | head -n 1)
              if [ -n "$prev_tag" ]; then
                notes=$(git log --pretty=format:"- %s" "$prev_tag"..HEAD)
                echo -e "Changes since $prev_tag:\n$notes" > release_notes.txt
              else
                echo "Release $CM_TAG" > release_notes.txt
              fi
            fi
          else
            echo "curl not available, using tag-based release notes"
            echo "Release $CM_TAG" > release_notes.txt
          fi
          echo "Generated release notes:"
          cat release_notes.txt

      - name: Get Flutter packages
        script: |
          flutter packages pub get

      - name: Flutter analyze
        script: |
          flutter analyze || true

      - name: Flutter unit tests
        script: |
          flutter test || true
        ignore_failure: true



      - name: Install CocoaPods dependencies
        script: |
          cd ios
          rm -rf Pods Podfile.lock
          pod install
          cd ..

      - name: Build iOS
        script: |
          flutter build ios --release --no-codesign --build-name=$BUILD_NAME --build-number=$BUILD_NUMBER
          xcode-project use-profiles
          xcode-project build-ipa \
            --workspace ios/Runner.xcworkspace \
            --scheme Runner

    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - flutter_drive.log

    publishing:
      slack:
        channel: "#builds"
        notify_on_build_start: true
        notify:
          success: true
          failure: true

      app_store_connect:
        auth: integration
        submit_to_testflight: true
        submit_to_app_store: false