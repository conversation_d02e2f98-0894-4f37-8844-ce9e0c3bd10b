<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>960655030374-02fgkj1ei4v8vsmrh0oldpu37pmd9n51.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.960655030374-02fgkj1ei4v8vsmrh0oldpu37pmd9n51</string>
	<key>API_KEY</key>
	<string>AIzaSyBdFpLo9PPwPu90v2HCq2EP63gIQmyYi_M</string>
	<key>GCM_SENDER_ID</key>
	<string>960655030374</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.novasoul.app</string>
	<key>PROJECT_ID</key>
	<string>nova-soul</string>
	<key>STORAGE_BUCKET</key>
	<string>nova-soul.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:960655030374:ios:f969c191a4e17e2de295a7</string>
</dict>
</plist>