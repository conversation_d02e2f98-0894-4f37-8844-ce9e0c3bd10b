# RevenueCat Setup Guide for Nova Soul

This comprehensive guide will walk you through setting up RevenueCat for the Nova Soul app's subscription system.

## 📋 Prerequisites

- Apple Developer Account (for iOS)
- Google Play Console Account (for Android)
- RevenueCat Account (free to start)
- Nova Soul app project

## 🚀 Step 1: Create RevenueCat Account

1. **Sign up for RevenueCat**
   - Go to [https://www.revenuecat.com/](https://www.revenuecat.com/)
   - Click "Get Started Free"
   - Sign up with your email or GitHub account

2. **Create a New Project**
   - After signing in, click "Create New Project"
   - Project Name: `Nova Soul`
   - Select your platform(s): iOS and/or Android

## 🍎 Step 2: iOS App Store Connect Setup

### 2.1 Create App in App Store Connect

1. **Login to App Store Connect**
   - Go to [https://appstoreconnect.apple.com/](https://appstoreconnect.apple.com/)
   - Sign in with your Apple Developer account

2. **Create New App**
   - Click "My Apps" → "+" → "New App"
   - Platform: iOS
   - Name: Nova Soul
   - Primary Language: English
   - Bundle ID: `com.yourcompany.novasoul` (use your actual bundle ID)
   - SKU: `nova-soul-ios`

### 2.2 Create Subscription Products

1. **Navigate to Subscriptions**
   - In your app, go to "Features" → "In-App Purchases"
   - Click "+" → "Auto-Renewable Subscription"

2. **Create Subscription Group**
   - Reference Name: `Nova Soul Premium`
   - App Name: `Nova Soul Premium`

3. **Create Individual Subscriptions**

   **Plus Subscription:**
   - Product ID: `nova_soul_plus_monthly`
   - Reference Name: `Nova Soul Plus Monthly`
   - Duration: 1 Month
   - Price: $4.99 USD
   - Localized Description: "Unlock AI memories and exclusive scenarios"

   **Pro Subscription:**
   - Product ID: `nova_soul_pro_monthly`
   - Reference Name: `Nova Soul Pro Monthly`
   - Duration: 1 Month
   - Price: $9.99 USD
   - Localized Description: "All Plus features + custom profile pics and image sending"

   **Elite Subscription:**
   - Product ID: `nova_soul_elite_monthly`
   - Reference Name: `Nova Soul Elite Monthly`
   - Duration: 1 Month
   - Price: $14.99 USD
   - Localized Description: "Unlimited access to all premium features"

4. **Add Localizations**
   - For each subscription, add localizations for your target markets
   - Include display names and descriptions in each language

5. **Submit for Review**
   - Once all subscriptions are configured, submit them for review
   - Note: You can test in sandbox before approval

## 🤖 Step 3: Google Play Console Setup (Android)

### 3.1 Create App in Google Play Console

1. **Login to Google Play Console**
   - Go to [https://play.google.com/console/](https://play.google.com/console/)
   - Sign in with your Google Developer account

2. **Create New App**
   - Click "Create app"
   - App name: Nova Soul
   - Default language: English
   - App or game: App
   - Free or paid: Free (with in-app purchases)

### 3.2 Create Subscription Products

1. **Navigate to Monetization**
   - In your app, go to "Monetize" → "Products" → "Subscriptions"
   - Click "Create subscription"

2. **Create Subscriptions**

   **Plus Subscription:**
   - Product ID: `nova_soul_plus_monthly`
   - Name: Nova Soul Plus
   - Description: "Unlock AI memories and exclusive scenarios"
   - Billing period: Monthly
   - Price: $4.99 USD

   **Pro Subscription:**
   - Product ID: `nova_soul_pro_monthly`
   - Name: Nova Soul Pro
   - Description: "All Plus features + custom profile pics and image sending"
   - Billing period: Monthly
   - Price: $9.99 USD

   **Elite Subscription:**
   - Product ID: `nova_soul_elite_monthly`
   - Name: Nova Soul Elite
   - Description: "Unlimited access to all premium features"
   - Billing period: Monthly
   - Price: $14.99 USD

3. **Activate Subscriptions**
   - After creating, activate each subscription
   - They'll be available for testing immediately

## 🔧 Step 4: Configure RevenueCat

### 4.1 Add Apps to RevenueCat

1. **Add iOS App**
   - In RevenueCat dashboard, go to "Apps"
   - Click "Add new app"
   - Platform: iOS
   - App name: Nova Soul iOS
   - Bundle ID: `com.yourcompany.novasoul`
   - App Store Connect App-Specific Shared Secret:
     - Go to App Store Connect → Your App → App Information
     - Copy the "App-Specific Shared Secret"
     - Paste it in RevenueCat

2. **Add Android App** (if applicable)
   - Platform: Android
   - App name: Nova Soul Android
   - Package name: `com.yourcompany.novasoul`
   - Google Play Service Account JSON:
     - Follow RevenueCat's guide to create service account
     - Upload the JSON file

### 4.2 Configure Products

1. **Create Entitlements**
   - Go to "Entitlements" in RevenueCat
   - Create entitlements:
     - `plus_features` - For Plus tier
     - `pro_features` - For Pro tier  
     - `elite_features` - For Elite tier

2. **Create Offerings**
   - Go to "Offerings"
   - Create offering: `default`
   - Add packages:
     - `nova_soul_plus_monthly` → `plus_features`
     - `nova_soul_pro_monthly` → `pro_features`
     - `nova_soul_elite_monthly` → `elite_features`

### 4.3 Get API Keys

1. **Public API Key**
   - Go to "API Keys" in RevenueCat
   - Copy the "Public API Key"
   - This goes in your app code

2. **Secret API Key**
   - Copy the "Secret API Key"
   - This is for server-side operations (keep secure)

## 📱 Step 5: Update App Configuration

### 5.1 Update Subscription Service

Replace the placeholder in `lib/core/services/subscription_service.dart`:

```dart
static const String _revenueCatApiKey = 'YOUR_ACTUAL_REVENUECAT_PUBLIC_API_KEY';
```

### 5.2 Update Product IDs (if different)

If you used different product IDs, update them in:
- `lib/core/services/subscription_service.dart`
- `lib/features/premium/presentation/pages/premium_page.dart`

## 🧪 Step 6: Testing

### 6.1 Sandbox Testing (iOS)

1. **Create Sandbox Test User**
   - App Store Connect → Users and Access → Sandbox Testers
   - Create test account with unique email

2. **Test on Device**
   - Sign out of App Store on test device
   - Install your app (TestFlight or development build)
   - Test subscription flow
   - Sign in with sandbox account when prompted

### 6.2 Internal Testing (Android)

1. **Create Internal Test Track**
   - Google Play Console → Testing → Internal testing
   - Upload your APK/AAB
   - Add test users

2. **Test Subscriptions**
   - Install app from internal test track
   - Test subscription purchases
   - Verify in Google Play Console

### 6.3 RevenueCat Testing

1. **Check Dashboard**
   - Monitor transactions in RevenueCat dashboard
   - Verify entitlements are granted correctly
   - Check customer profiles

2. **Test Restore Purchases**
   - Test the "Restore Purchases" functionality
   - Verify it works across app reinstalls

## 🔒 Step 7: Security & Production

### 7.1 Environment Configuration

Create different RevenueCat projects for:
- Development/Testing
- Production

### 7.2 Server-Side Validation

Consider implementing server-side receipt validation for additional security:
- Use RevenueCat webhooks
- Validate purchases on your backend
- Store subscription status in your database

### 7.3 Privacy & Compliance

1. **Update Privacy Policy**
   - Include subscription terms
   - Mention RevenueCat data processing
   - Add cancellation policy

2. **App Store Guidelines**
   - Ensure compliance with App Store Review Guidelines
   - Include clear subscription terms in app

## 📊 Step 8: Analytics & Monitoring

### 8.1 RevenueCat Charts

Monitor key metrics:
- Monthly Recurring Revenue (MRR)
- Churn rate
- Conversion rates
- Customer lifetime value

### 8.2 Custom Analytics

Integrate with your analytics platform:
- Track subscription events
- Monitor user behavior
- A/B test pricing strategies

## 🚨 Troubleshooting

### Common Issues

1. **"No products available"**
   - Check product IDs match exactly
   - Ensure products are approved (iOS) or active (Android)
   - Verify RevenueCat configuration

2. **Purchases not working**
   - Check API keys are correct
   - Verify app bundle ID matches
   - Test with sandbox/internal testing accounts

3. **Restore not working**
   - Ensure user is signed in to same account
   - Check RevenueCat customer ID handling
   - Verify entitlements configuration

### Support Resources

- RevenueCat Documentation: [https://docs.revenuecat.com/](https://docs.revenuecat.com/)
- RevenueCat Community: [https://community.revenuecat.com/](https://community.revenuecat.com/)
- Apple Developer Support
- Google Play Developer Support

## ✅ Launch Checklist

Before going live:

- [ ] All subscription products approved/active
- [ ] RevenueCat properly configured
- [ ] Sandbox/internal testing completed
- [ ] Privacy policy updated
- [ ] Terms of service include subscription terms
- [ ] Customer support process for subscriptions
- [ ] Monitoring and analytics set up
- [ ] Backup payment processing plan

## 🎉 You're Ready!

Your Nova Soul app now has a fully functional subscription system powered by RevenueCat! 

The system includes:
- ✅ 4-tier subscription model (FREE, PLUS, PRO, ELITE)
- ✅ Feature gating and usage limits
- ✅ Purchase and restore functionality
- ✅ Error handling and user feedback
- ✅ Loading states and UI polish
- ✅ Cross-platform support

Happy monetizing! 💰
