import 'package:flutter_test/flutter_test.dart';
import 'package:nova_soul/shared/models/subscription_tier.dart';
import 'package:nova_soul/shared/models/usage_tracking.dart';

void main() {
  group('Subscription System Tests', () {
    test('Subscription tiers have correct hierarchy', () {
      expect(SubscriptionTier.free.index, equals(0));
      expect(SubscriptionTier.plus.index, equals(1));
      expect(SubscriptionTier.pro.index, equals(2));
      expect(SubscriptionTier.elite.index, equals(3));
    });

    test('Subscription tier display names are correct', () {
      expect(SubscriptionTier.free.displayName, equals('FREE'));
      expect(SubscriptionTier.plus.displayName, equals('Plus'));
      expect(SubscriptionTier.pro.displayName, equals('Pro'));
      expect(SubscriptionTier.elite.displayName, equals('Elite'));
    });

    test('Subscription tier emojis are correct', () {
      expect(SubscriptionTier.free.emoji, equals('🏅'));
      expect(SubscriptionTier.plus.emoji, equals('🥉'));
      expect(SubscriptionTier.pro.emoji, equals('🥈'));
      expect(SubscriptionTier.elite.emoji, equals('🥇'));
    });

    test('FREE tier has correct limits', () {
      final limits = SubscriptionLimits.getLimits(SubscriptionTier.free);
      
      expect(limits.messagesPerDay, equals(20));
      expect(limits.companionLimit, equals(1));
      expect(limits.imagesPerDay, equals(0));
      expect(limits.hasMemory, isFalse);
      expect(limits.hasProfilePic, isFalse);
      expect(limits.allowedScenarios, isEmpty);
      expect(limits.allowedGames, isEmpty);
    });

    test('PLUS tier has correct limits', () {
      final limits = SubscriptionLimits.getLimits(SubscriptionTier.plus);

      expect(limits.messagesPerDay, equals(100));
      expect(limits.companionLimit, equals(2)); // 2 companions
      expect(limits.imagesPerDay, equals(0));
      expect(limits.hasMemory, isTrue);
      expect(limits.hasProfilePic, isFalse);
      expect(limits.allowedScenarios, contains('comfort_me'));
      expect(limits.allowedScenarios, contains('mental_health_checkin'));
      expect(limits.allowedGames, isEmpty);
    });

    test('PRO tier has correct limits', () {
      final limits = SubscriptionLimits.getLimits(SubscriptionTier.pro);

      expect(limits.messagesPerDay, equals(300));
      expect(limits.companionLimit, equals(4)); // 4 companions
      expect(limits.imagesPerDay, equals(1));
      expect(limits.hasMemory, isTrue);
      expect(limits.hasProfilePic, isTrue);
      expect(limits.allowedScenarios.length, equals(6));
      expect(limits.allowedGames, contains('truth_dare'));
      expect(limits.allowedGames, contains('would_rather'));
    });

    test('ELITE tier has correct limits', () {
      final limits = SubscriptionLimits.getLimits(SubscriptionTier.elite);
      
      expect(limits.messagesPerDay, equals(-1)); // unlimited
      expect(limits.companionLimit, equals(-1)); // unlimited
      expect(limits.imagesPerDay, equals(-1)); // unlimited
      expect(limits.hasMemory, isTrue);
      expect(limits.hasProfilePic, isTrue);
      expect(limits.allowedScenarios.length, equals(6));
      expect(limits.allowedGames, contains('truth_dare'));
      expect(limits.allowedGames, contains('would_rather'));
    });

    test('Subscription tier from string works correctly', () {
      expect(SubscriptionTier.fromString('PLUS'), equals(SubscriptionTier.plus));
      expect(SubscriptionTier.fromString('PRO'), equals(SubscriptionTier.pro));
      expect(SubscriptionTier.fromString('ELITE'), equals(SubscriptionTier.elite));
      expect(SubscriptionTier.fromString('INVALID'), equals(SubscriptionTier.free));
      expect(SubscriptionTier.fromString(null), equals(SubscriptionTier.free));
    });

    test('Usage status correctly identifies limits', () {
      const usage = UsageStatus(
        canSendMessage: false,
        canSendImage: true,
        canCreateCompanion: false,
        remainingMessages: 0,
        remainingImages: 5,
        limitMessage: 'Daily message limit reached',
      );

      expect(usage.canSendMessage, isFalse);
      expect(usage.canSendImage, isTrue);
      expect(usage.canCreateCompanion, isFalse);
      expect(usage.remainingMessages, equals(0));
      expect(usage.remainingImages, equals(5));
    });

    test('Daily usage tracks correctly', () {
      final now = DateTime.now();
      final usage = DailyUsage(
        userId: 'test_user',
        date: now,
        messagesSent: 15,
        imagesSent: 2,
        lastReset: now,
      );

      expect(usage.userId, equals('test_user'));
      expect(usage.messagesSent, equals(15));
      expect(usage.imagesSent, equals(2));
    });

    test('Subscription limits helper methods work', () {
      final freeLimits = SubscriptionLimits.getLimits(SubscriptionTier.free);
      final eliteLimits = SubscriptionLimits.getLimits(SubscriptionTier.elite);

      expect(freeLimits.hasUnlimitedMessages, isFalse);
      expect(freeLimits.hasUnlimitedImages, isFalse);
      expect(freeLimits.hasUnlimitedCompanions, isFalse);

      expect(eliteLimits.hasUnlimitedMessages, isTrue);
      expect(eliteLimits.hasUnlimitedImages, isTrue);
      expect(eliteLimits.hasUnlimitedCompanions, isTrue);
    });
  });
}
